/* WorkflowCapture Pro - Screenshot Review Page Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
  background: #f8f9fa;
  overflow-x: hidden;
}

.review-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.review-header {
  background: white;
  border-bottom: 1px solid #e1e8ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.header-text h1 {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.session-info {
  font-size: 13px;
  color: #7f8c8d;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* Buttons */
.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-outline {
  background: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Main Content */
.review-content {
  flex: 1;
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  gap: 24px;
  padding: 24px;
}

/* Sidebar */
.review-sidebar {
  width: 300px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: fit-content;
  max-height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.sidebar-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.screenshot-count {
  background: #3498db;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.thumbnail-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  overflow-y: auto;
  max-height: calc(100vh - 220px);
}

.thumbnail-item {
  background: #f8f9fa;
  border: 2px solid transparent;
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.thumbnail-item:hover {
  border-color: #3498db;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.thumbnail-item.active {
  border-color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}

.thumbnail-image {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 8px;
}

.thumbnail-info {
  text-align: center;
}

.thumbnail-sequence {
  font-size: 11px;
  font-weight: 600;
  color: #3498db;
  margin-bottom: 2px;
}

.thumbnail-time {
  font-size: 10px;
  color: #7f8c8d;
}

/* Main View */
.review-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.screenshot-viewer {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  text-align: center;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  color: #95a5a6;
}

.empty-state svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #7f8c8d;
}

.empty-state p {
  font-size: 14px;
  line-height: 1.4;
}

.screenshot-display {
  max-width: 100%;
  max-height: 600px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

/* Screenshot Details */
.screenshot-details {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.details-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.details-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.details-actions {
  display: flex;
  gap: 8px;
}

.details-content {
  padding: 20px;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.detail-item label {
  font-weight: 600;
  color: #34495e;
  min-width: 100px;
  margin-right: 12px;
}

.detail-item span {
  color: #7f8c8d;
  word-break: break-all;
}

.comment-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

.comment-section label {
  display: block;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 8px;
}

.comment-section textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  font-size: 13px;
  font-family: inherit;
  resize: vertical;
  margin-bottom: 12px;
}

.comment-section textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Progress Overlay */
.progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.progress-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.progress-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e1e8ed;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  width: 0%;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #7f8c8d;
}

/* Advanced Editor Modal */
.advanced-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.editor-overlay {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.editor-container {
  background: white;
  border-radius: 12px;
  max-width: 95vw;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.editor-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.close-editor {
  background: transparent;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  color: #7f8c8d;
  transition: all 0.2s ease;
}

.close-editor:hover {
  background: #e1e8ed;
  color: #e74c3c;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
  flex-wrap: wrap;
}

.tool-group {
  display: flex;
  gap: 4px;
}

.tool-btn {
  background: white;
  border: 1px solid #e1e8ed;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  color: #7f8c8d;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-btn:hover {
  border-color: #3498db;
  color: #3498db;
}

.tool-btn.active {
  background: #3498db;
  border-color: #3498db;
  color: white;
}

.tool-options {
  display: flex;
  gap: 16px;
  align-items: center;
}

.option-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-group label {
  font-size: 12px;
  font-weight: 600;
  color: #34495e;
}

.option-group input[type="color"] {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.option-group input[type="range"] {
  width: 80px;
}

.option-group select {
  padding: 4px 8px;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  font-size: 12px;
}

.layer-controls {
  display: flex;
  gap: 4px;
  margin-left: auto;
}

.editor-canvas-container {
  flex: 1;
  padding: 20px;
  text-align: center;
  overflow: auto;
  background: #f8f9fa;
}

.editor-canvas-container canvas {
  max-width: 100%;
  max-height: 100%;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  background: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.editor-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e1e8ed;
  background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .review-content {
    flex-direction: column;
  }
  
  .review-sidebar {
    width: 100%;
    max-height: 300px;
  }
  
  .thumbnail-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    max-height: 200px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .review-content {
    padding: 16px;
  }
  
  .editor-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .tool-options {
    flex-wrap: wrap;
  }
}

/* Animation for click indicators */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.thumbnail-item {
  animation: fadeInScale 0.3s ease-out;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #3498db;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
