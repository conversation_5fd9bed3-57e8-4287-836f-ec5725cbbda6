<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WorkflowCapture Pro - Screenshot Review</title>
    <link rel="stylesheet" href="screenshot-review.css">
</head>
<body>
    <div class="review-container">
        <!-- Header -->
        <header class="review-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="header-text">
                        <h1>Screenshot Review</h1>
                        <p class="session-info" id="session-info">Loading session...</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn btn-outline" id="edit-all-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
                            <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
                        </svg>
                        Edit All
                    </button>
                    <button class="btn btn-primary" id="export-pdf-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                        Export PDF
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="review-content">
            <!-- Sidebar -->
            <aside class="review-sidebar">
                <div class="sidebar-header">
                    <h3>Screenshots</h3>
                    <span class="screenshot-count" id="screenshot-count">0</span>
                </div>
                <div class="thumbnail-grid" id="thumbnail-grid">
                    <!-- Thumbnails will be populated here -->
                </div>
            </aside>

            <!-- Main View -->
            <section class="review-main">
                <div class="screenshot-viewer" id="screenshot-viewer">
                    <div class="empty-state">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <path d="M23 19a2 2 0 01-2 2H3a2 2 0 01-2-2V8a2 2 0 012-2h4l2-3h6l2 3h4a2 2 0 012 2z"/>
                            <circle cx="12" cy="13" r="4"/>
                        </svg>
                        <h3>Select a screenshot to view</h3>
                        <p>Click on any thumbnail to view and edit the screenshot</p>
                    </div>
                </div>

                <!-- Screenshot Details Panel -->
                <div class="screenshot-details" id="screenshot-details" style="display: none;">
                    <div class="details-header">
                        <h3>Screenshot Details</h3>
                        <div class="details-actions">
                            <button class="btn btn-outline btn-sm" id="edit-screenshot-btn">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
                                    <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                </svg>
                                Edit
                            </button>
                            <button class="btn btn-outline btn-sm" id="download-screenshot-btn">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                Download
                            </button>
                        </div>
                    </div>
                    
                    <div class="details-content">
                        <div class="detail-item">
                            <label>Sequence:</label>
                            <span id="detail-sequence">-</span>
                        </div>
                        <div class="detail-item">
                            <label>Timestamp:</label>
                            <span id="detail-timestamp">-</span>
                        </div>
                        <div class="detail-item">
                            <label>Page URL:</label>
                            <span id="detail-url">-</span>
                        </div>
                        <div class="detail-item">
                            <label>Element:</label>
                            <span id="detail-element">-</span>
                        </div>
                        
                        <div class="comment-section">
                            <label for="screenshot-comment">Comments:</label>
                            <textarea 
                                id="screenshot-comment" 
                                placeholder="Add your comments about this screenshot..."
                                rows="4"
                            ></textarea>
                            <button class="btn btn-sm btn-primary" id="save-comment-btn">Save Comment</button>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Progress Bar for PDF Export -->
        <div class="progress-overlay" id="progress-overlay" style="display: none;">
            <div class="progress-content">
                <h3>Generating PDF...</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <p class="progress-text" id="progress-text">Preparing screenshots...</p>
            </div>
        </div>
    </div>

    <!-- Advanced Screenshot Editor Modal -->
    <div class="advanced-editor-modal" id="advanced-editor-modal" style="display: none;">
        <div class="editor-overlay">
            <div class="editor-container">
                <div class="editor-header">
                    <h3>Advanced Screenshot Editor</h3>
                    <button class="close-editor" id="close-advanced-editor">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                    </button>
                </div>
                
                <div class="editor-toolbar">
                    <div class="tool-group">
                        <button class="tool-btn active" data-tool="select" title="Select">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"/>
                            </svg>
                        </button>
                        <button class="tool-btn" data-tool="text" title="Text">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="4,7 4,4 20,4 20,7"/>
                                <line x1="9" y1="20" x2="15" y2="20"/>
                                <line x1="12" y1="4" x2="12" y2="20"/>
                            </svg>
                        </button>
                        <button class="tool-btn" data-tool="arrow" title="Arrow">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="5" y1="12" x2="19" y2="12"/>
                                <polyline points="12,5 19,12 12,19"/>
                            </svg>
                        </button>
                        <button class="tool-btn" data-tool="rectangle" title="Rectangle">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            </svg>
                        </button>
                        <button class="tool-btn" data-tool="circle" title="Circle">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                            </svg>
                        </button>
                        <button class="tool-btn" data-tool="highlight" title="Highlight">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 11H1l6-6 6 6z"/>
                                <path d="M9 17l3 3 3-3"/>
                                <path d="M22 12h-8"/>
                                <path d="M3 21h18"/>
                            </svg>
                        </button>
                        <button class="tool-btn" data-tool="draw" title="Draw">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 19l7-7 3 3-7 7-3-3z"/>
                                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/>
                                <path d="M2 2l7.586 7.586"/>
                                <circle cx="11" cy="11" r="2"/>
                            </svg>
                        </button>
                        <button class="tool-btn" data-tool="blur" title="Blur/Redact">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                            </svg>
                        </button>
                        <button class="tool-btn" data-tool="crop" title="Crop">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M6.13 1L6 16a2 2 0 002 2h15"/>
                                <path d="M1 6.13L16 6a2 2 0 012 2v15"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="tool-options">
                        <div class="option-group">
                            <label>Color:</label>
                            <input type="color" id="tool-color" value="#ff0000">
                        </div>
                        <div class="option-group">
                            <label>Size:</label>
                            <input type="range" id="tool-size" min="1" max="20" value="3">
                            <span id="size-value">3</span>
                        </div>
                        <div class="option-group">
                            <label>Font:</label>
                            <select id="tool-font">
                                <option value="Arial">Arial</option>
                                <option value="Helvetica">Helvetica</option>
                                <option value="Times New Roman">Times New Roman</option>
                                <option value="Courier New">Courier New</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layer-controls">
                        <button class="btn btn-sm" id="undo-btn" title="Undo">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 7v6h6"/>
                                <path d="M21 17a9 9 0 00-9-9 9 9 0 00-6 2.3L3 13"/>
                            </svg>
                        </button>
                        <button class="btn btn-sm" id="redo-btn" title="Redo">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 7v6h-6"/>
                                <path d="M3 17a9 9 0 019-9 9 9 0 016 2.3L21 13"/>
                            </svg>
                        </button>
                        <button class="btn btn-sm" id="clear-all-btn" title="Clear All">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"/>
                                <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <div class="editor-canvas-container">
                    <canvas id="advanced-editor-canvas"></canvas>
                </div>
                
                <div class="editor-actions">
                    <button class="btn btn-outline" id="cancel-advanced-edit">Cancel</button>
                    <button class="btn btn-primary" id="save-advanced-edit">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <script src="screenshot-review.js"></script>
</body>
</html>
