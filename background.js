// Background script for My Own Scribe extension
let isCapturing = false;
let notes = [];

// Initialize storage
chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.sync.get(['notes'], (result) => {
    if (result.notes) {
      notes = result.notes;
    }
  });
});

// Handle messages from content script and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'TOGGLE_CAPTURE':
      isCapturing = message.isCapturing;
      sendResponse({ success: true });
      break;

    case 'GET_CAPTURE_STATUS':
      sendResponse({ isCapturing });
      break;

    case 'SAVE_NOTE':
      const newNote = {
        ...message.note,
        timestamp: Date.now()
      };
      notes.unshift(newNote); // Add to beginning of array
      saveNotes();
      chrome.runtime.sendMessage({ type: 'NEW_NOTE', note: newNote });
      sendResponse({ success: true });
      break;

    case 'GET_NOTES':
      sendResponse({ notes });
      break;

    case 'CLEAR_NOTES':
      notes = [];
      saveNotes();
      sendResponse({ success: true });
      break;

    default:
      sendResponse({ error: 'Unknown message type' });
  }

  // Required for async sendResponse
  return true;
});

// Save notes to storage
function saveNotes() {
  chrome.storage.sync.set({ notes }, () => {
    if (chrome.runtime.lastError) {
      console.error('Error saving notes:', chrome.runtime.lastError);
    }
  });
}

// Set up context menu item
chrome.contextMenus.create({
  id: 'capture-selection',
  title: 'Save selection to My Own Scribe',
  contexts: ['selection']
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'capture-selection' && info.selectionText) {
    chrome.tabs.sendMessage(tab.id, {
      type: 'CAPTURE_SELECTION',
      selection: info.selectionText
    });
  }
});
