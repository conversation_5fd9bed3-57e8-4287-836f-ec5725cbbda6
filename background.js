/**
 * WorkflowCapture Pro - Background Script
 * Handles workflow storage, screenshot capture, and cross-tab communication
 */

class WorkflowCaptureBackground {
  constructor() {
    this.isCapturing = false;
    this.notes = [];
    this.workflows = [];
    this.currentWorkflow = null;
    this.screenshots = new Map();

    this.init();
  }

  async init() {
    // Initialize storage
    await this.loadData();

    // Set up event listeners
    this.setupEventListeners();

    // Set up context menus
    this.setupContextMenus();

    console.log('WorkflowCapture Pro background initialized');
  }

  async loadData() {
    try {
      const result = await chrome.storage.local.get(['notes', 'workflows', 'isCapturing']);
      this.notes = result.notes || [];
      this.workflows = result.workflows || [];
      this.isCapturing = result.isCapturing || false;
    } catch (error) {
      console.error('Error loading data:', error);
    }
  }

  setupEventListeners() {
    // Handle messages from content scripts and popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Handle extension installation
    chrome.runtime.onInstalled.addListener(() => {
      this.handleInstallation();
    });

    // Handle tab updates for workflow tracking
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (this.isCapturing && changeInfo.status === 'complete') {
        this.handleTabUpdate(tabId, tab);
      }
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'TOGGLE_CAPTURE':
          await this.toggleCapture(message.isCapturing);
          sendResponse({ success: true });
          break;

        case 'GET_CAPTURE_STATUS':
          sendResponse({ isCapturing: this.isCapturing });
          break;

        case 'START_RECORDING':
          const tab = message.tab || sender.tab;
          await this.startRecording(tab);
          sendResponse({ success: true });
          break;

        case 'STOP_RECORDING':
          await this.stopRecording();
          sendResponse({ success: true });
          break;

        case 'SAVE_WORKFLOW':
          await this.saveWorkflow(message.workflow);
          sendResponse({ success: true });
          break;

        case 'GET_WORKFLOWS':
          sendResponse({ workflows: this.workflows });
          break;

        case 'DELETE_WORKFLOW':
          await this.deleteWorkflow(message.workflowId);
          sendResponse({ success: true });
          break;

        case 'CAPTURE_SCREENSHOT':
          try {
            const tab = message.tab || sender.tab;
            const screenshot = await this.captureScreenshot(tab, message.context);
            sendResponse({ screenshot });
          } catch (error) {
            console.error('Screenshot capture failed:', error);
            sendResponse({ error: error.message });
          }
          break;

        case 'SAVE_NOTE':
          await this.saveNote(message.note);
          sendResponse({ success: true });
          break;

        case 'GET_NOTES':
          sendResponse({ notes: this.notes });
          break;

        case 'CLEAR_NOTES':
          await this.clearNotes();
          sendResponse({ success: true });
          break;

        case 'SEARCH_WORKFLOWS':
          const results = await this.searchWorkflows(message.query);
          sendResponse({ results });
          break;

        default:
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message });
    }
  }

  async toggleCapture(isCapturing) {
    this.isCapturing = isCapturing;
    await this.saveData();

    // Notify all content scripts
    const tabs = await chrome.tabs.query({});
    tabs.forEach(tab => {
      chrome.tabs.sendMessage(tab.id, {
        type: 'CAPTURE_STATUS_UPDATE',
        isCapturing: this.isCapturing
      }).catch(() => {
        // Ignore errors for tabs that don't have content script
      });
    });
  }

  async startRecording(tab) {
    this.isCapturing = true;
    this.currentWorkflow = {
      id: this.generateId(),
      title: tab.title,
      url: tab.url,
      startTime: Date.now(),
      tabId: tab.id
    };

    await this.saveData();

    // Notify content script
    chrome.tabs.sendMessage(tab.id, {
      type: 'START_RECORDING'
    });
  }

  async stopRecording() {
    this.isCapturing = false;
    this.currentWorkflow = null;
    await this.saveData();
  }

  async saveWorkflow(workflow) {
    // Add workflow to storage
    this.workflows.unshift(workflow);

    // Keep only last 100 workflows to prevent storage bloat
    if (this.workflows.length > 100) {
      this.workflows = this.workflows.slice(0, 100);
    }

    await this.saveData();

    // Notify popup of new workflow
    this.notifyPopup({ type: 'NEW_WORKFLOW', workflow });
  }

  async deleteWorkflow(workflowId) {
    this.workflows = this.workflows.filter(w => w.id !== workflowId);
    await this.saveData();
  }

  async captureScreenshot(tab, context) {
    try {
      // Capture visible tab
      const dataUrl = await chrome.tabs.captureVisibleTab(tab.windowId, {
        format: 'png',
        quality: 90
      });

      // Store screenshot with context
      const screenshotId = this.generateId();
      this.screenshots.set(screenshotId, {
        dataUrl,
        context,
        timestamp: Date.now(),
        tabId: tab.id,
        url: tab.url
      });

      // Clean up old screenshots (keep only last 50)
      if (this.screenshots.size > 50) {
        const entries = Array.from(this.screenshots.entries());
        const oldEntries = entries.slice(0, entries.length - 50);
        oldEntries.forEach(([id]) => this.screenshots.delete(id));
      }

      return {
        id: screenshotId,
        dataUrl: dataUrl,
        context: context,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Screenshot capture failed:', error);
      throw error;
    }
  }

  async saveNote(note) {
    const newNote = {
      ...note,
      id: this.generateId(),
      timestamp: Date.now()
    };

    this.notes.unshift(newNote);

    // Keep only last 1000 notes
    if (this.notes.length > 1000) {
      this.notes = this.notes.slice(0, 1000);
    }

    await this.saveData();

    // Notify popup
    this.notifyPopup({ type: 'NEW_NOTE', note: newNote });
  }

  async clearNotes() {
    this.notes = [];
    await this.saveData();
  }

  async searchWorkflows(query) {
    if (!query || query.trim() === '') {
      return this.workflows;
    }

    const searchTerm = query.toLowerCase();
    return this.workflows.filter(workflow => {
      return (
        workflow.title?.toLowerCase().includes(searchTerm) ||
        workflow.url?.toLowerCase().includes(searchTerm) ||
        workflow.steps?.some(step =>
          step.description?.toLowerCase().includes(searchTerm)
        )
      );
    });
  }

  async saveData() {
    try {
      await chrome.storage.local.set({
        notes: this.notes,
        workflows: this.workflows,
        isCapturing: this.isCapturing
      });
    } catch (error) {
      console.error('Error saving data:', error);
    }
  }

  notifyPopup(message) {
    // Try to send message to popup (will fail silently if popup is closed)
    chrome.runtime.sendMessage(message).catch(() => {
      // Popup is not open, ignore error
    });
  }

  handleInstallation() {
    // Set up initial state
    console.log('WorkflowCapture Pro installed');
  }

  handleTabUpdate(tabId, tab) {
    // Track navigation during workflow recording
    if (this.currentWorkflow && this.currentWorkflow.tabId === tabId) {
      chrome.tabs.sendMessage(tabId, {
        type: 'TAB_UPDATED',
        url: tab.url,
        title: tab.title
      }).catch(() => {
        // Content script might not be ready yet
      });
    }
  }

  setupContextMenus() {
    // Create context menu for text selection
    chrome.contextMenus.create({
      id: 'capture-selection',
      title: 'Save selection to WorkflowCapture Pro',
      contexts: ['selection']
    });

    // Create context menu for workflow actions
    chrome.contextMenus.create({
      id: 'start-workflow',
      title: 'Start recording workflow',
      contexts: ['page']
    });

    // Handle context menu clicks
    chrome.contextMenus.onClicked.addListener(async (info, tab) => {
      try {
        if (info.menuItemId === 'capture-selection' && info.selectionText) {
          await chrome.tabs.sendMessage(tab.id, {
            type: 'CAPTURE_SELECTION',
            selection: info.selectionText
          });
        } else if (info.menuItemId === 'start-workflow') {
          await this.startRecording(tab);
        }
      } catch (error) {
        console.error('Context menu action failed:', error);
      }
    });
  }

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
}

// Initialize the background service
const workflowCaptureBackground = new WorkflowCaptureBackground();
