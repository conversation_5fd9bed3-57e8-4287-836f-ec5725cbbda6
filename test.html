<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WorkflowCapture Pro Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #34495e;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #95a5a6;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .result {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            display: none;
        }
        
        .navigation-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .navigation-links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
        }
        
        .navigation-links a:hover {
            text-decoration: underline;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #856404;
        }
        
        .instructions ol {
            margin-bottom: 0;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1>🎯 WorkflowCapture Pro Test Page</h1>
    
    <div class="instructions">
        <h3>📋 Enhanced Testing Instructions</h3>
        <ol>
            <li><strong>Install the extension</strong> in Chrome Developer Mode</li>
            <li><strong>Click the extension icon</strong> to open the popup</li>
            <li><strong>Start recording</strong> by clicking "Start Recording"</li>
            <li><strong>🆕 Click-based screenshots:</strong> Every click will automatically capture a screenshot</li>
            <li><strong>Perform actions</strong> on this page (fill forms, click buttons, etc.)</li>
            <li><strong>Stop recording</strong> - this will automatically open the Screenshot Review page</li>
            <li><strong>🆕 Review screenshots:</strong> View thumbnails, add comments, and edit screenshots</li>
            <li><strong>🆕 Advanced editing:</strong> Use text, arrows, highlights, blur tools, and more</li>
            <li><strong>🆕 Export PDF:</strong> Generate a professional PDF with all screenshots and comments</li>
        </ol>

        <div style="background: #e8f5e8; padding: 15px; border-radius: 6px; margin-top: 15px;">
            <h4 style="margin-top: 0; color: #27ae60;">🎯 New Features to Test:</h4>
            <ul style="margin-bottom: 0;">
                <li><strong>Click indicators:</strong> Red circles appear on every click during recording</li>
                <li><strong>Screenshot review page:</strong> Opens automatically after stopping recording</li>
                <li><strong>Advanced editor:</strong> Click any screenshot to open the editing interface</li>
                <li><strong>PDF export:</strong> Generate professional documentation with comments</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>📝 Sample Form</h2>
        <form id="testForm">
            <div class="form-group">
                <label for="firstName">First Name:</label>
                <input type="text" id="firstName" name="firstName" placeholder="Enter your first name">
            </div>
            
            <div class="form-group">
                <label for="lastName">Last Name:</label>
                <input type="text" id="lastName" name="lastName" placeholder="Enter your last name">
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" placeholder="Enter your email address">
            </div>
            
            <div class="form-group">
                <label for="department">Department:</label>
                <select id="department" name="department">
                    <option value="">Select a department</option>
                    <option value="engineering">Engineering</option>
                    <option value="marketing">Marketing</option>
                    <option value="sales">Sales</option>
                    <option value="hr">Human Resources</option>
                    <option value="finance">Finance</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="comments">Comments:</label>
                <textarea id="comments" name="comments" rows="4" placeholder="Enter any additional comments"></textarea>
            </div>
            
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="newsletter" name="newsletter">
                    <label for="newsletter">Subscribe to newsletter</label>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="terms" name="terms">
                    <label for="terms">I agree to the terms and conditions</label>
                </div>
            </div>
            
            <button type="submit" class="btn">Submit Form</button>
            <button type="reset" class="btn btn-secondary">Reset Form</button>
        </form>
        
        <div id="formResult" class="result">
            <strong>✅ Form submitted successfully!</strong>
            <p>Thank you for testing the WorkflowCapture Pro extension.</p>
        </div>
    </div>

    <div class="container">
        <h2>🔘 Interactive Buttons</h2>
        <p>Click these buttons to test different interactions:</p>
        
        <button class="btn" onclick="showAlert()">Show Alert</button>
        <button class="btn btn-success" onclick="changeBackground()">Change Background</button>
        <button class="btn btn-secondary" onclick="addListItem()">Add List Item</button>
        <button class="btn" onclick="toggleVisibility()">Toggle Content</button>
        
        <div id="dynamicContent" style="margin-top: 20px;">
            <h4>📋 Dynamic List</h4>
            <ul id="dynamicList">
                <li>Initial list item</li>
            </ul>
        </div>
        
        <div id="toggleableContent" style="margin-top: 20px; padding: 15px; background: #ecf0f1; border-radius: 6px;">
            <p>This content can be toggled on and off to test dynamic interactions.</p>
        </div>
    </div>

    <div class="navigation-links">
        <a href="#top">Back to Top</a>
        <a href="javascript:void(0)" onclick="window.location.reload()">Reload Page</a>
        <a href="https://www.example.com" target="_blank">External Link</a>
    </div>

    <script>
        // Form submission handler
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Simulate form processing
            setTimeout(() => {
                document.getElementById('formResult').style.display = 'block';
                document.getElementById('formResult').scrollIntoView({ behavior: 'smooth' });
            }, 500);
        });

        // Interactive button functions
        function showAlert() {
            alert('🎉 Alert triggered! This tests popup interactions.');
        }

        function changeBackground() {
            const colors = ['#f8f9fa', '#e3f2fd', '#f3e5f5', '#e8f5e8', '#fff3e0'];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            document.body.style.backgroundColor = randomColor;
        }

        function addListItem() {
            const list = document.getElementById('dynamicList');
            const newItem = document.createElement('li');
            newItem.textContent = `New item ${list.children.length}`;
            list.appendChild(newItem);
        }

        function toggleVisibility() {
            const content = document.getElementById('toggleableContent');
            content.style.display = content.style.display === 'none' ? 'block' : 'none';
        }

        // Add some dynamic behavior
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 WorkflowCapture Pro test page loaded');
            
            // Add hover effects to form inputs
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'scale(1.02)';
                });
                
                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
