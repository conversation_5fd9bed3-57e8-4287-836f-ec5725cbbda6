/**
 * WorkflowCapture Pro - Popup Script
 * Modern popup interface with tabbed navigation and advanced features
 */

class WorkflowCapturePopup {
  constructor() {
    this.isRecording = false;
    this.workflows = [];
    this.notes = [];
    this.currentTab = 'workflows';
    this.settings = {
      autoScreenshot: true,
      smartDescriptions: true,
      highlightElements: true
    };

    this.init();
  }

  async init() {
    // Load initial data
    await this.loadData();

    // Set up event listeners
    this.setupEventListeners();

    // Initialize UI
    this.updateUI();

    // Listen for background messages
    this.setupMessageListeners();

    console.log('WorkflowCapture Pro popup initialized');
  }

  async loadData() {
    try {
      // Get capture status
      const statusResponse = await this.sendMessage({ type: 'GET_CAPTURE_STATUS' });
      this.isRecording = statusResponse?.isCapturing || false;

      // Get workflows
      const workflowsResponse = await this.sendMessage({ type: 'GET_WORKFLOWS' });
      this.workflows = workflowsResponse?.workflows || [];

      // Get notes
      const notesResponse = await this.sendMessage({ type: 'GET_NOTES' });
      this.notes = notesResponse?.notes || [];

      // Load settings from storage
      const settings = await chrome.storage.local.get(['settings']);
      if (settings.settings) {
        this.settings = { ...this.settings, ...settings.settings };
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
  }

  setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tab = e.currentTarget.dataset.tab;
        this.switchTab(tab);
      });
    });

    // Recording toggle
    const toggleBtn = document.getElementById('toggle-recording');
    toggleBtn?.addEventListener('click', () => {
      this.toggleRecording();
    });

    // Import workflow
    const importBtn = document.getElementById('import-workflow');
    importBtn?.addEventListener('click', () => {
      this.importWorkflow();
    });

    // Search workflows
    const workflowSearch = document.getElementById('workflow-search');
    workflowSearch?.addEventListener('input', (e) => {
      this.searchWorkflows(e.target.value);
    });

    // Search notes
    const notesSearch = document.getElementById('notes-search');
    notesSearch?.addEventListener('input', (e) => {
      this.searchNotes(e.target.value);
    });

    // Clear notes
    const clearNotesBtn = document.getElementById('clear-notes');
    clearNotesBtn?.addEventListener('click', () => {
      this.clearNotes();
    });

    // Settings checkboxes
    document.querySelectorAll('#settings-tab input[type="checkbox"]').forEach(checkbox => {
      checkbox.addEventListener('change', (e) => {
        this.updateSetting(e.target.id, e.target.checked);
      });
    });

    // Export buttons
    const exportWorkflowsBtn = document.getElementById('export-workflows');
    exportWorkflowsBtn?.addEventListener('click', () => {
      this.exportWorkflows();
    });

    const exportNotesBtn = document.getElementById('export-notes');
    exportNotesBtn?.addEventListener('click', () => {
      this.exportNotes();
    });

    // Help link
    const helpLink = document.getElementById('help-link');
    helpLink?.addEventListener('click', (e) => {
      e.preventDefault();
      this.openHelp();
    });
  }

  setupMessageListeners() {
    chrome.runtime.onMessage.addListener((message) => {
      switch (message.type) {
        case 'NEW_WORKFLOW':
          this.workflows.unshift(message.workflow);
          this.updateWorkflowsList();
          this.updateWorkflowCount();
          break;

        case 'NEW_NOTE':
          this.notes.unshift(message.note);
          this.updateNotesList();
          this.updateNotesCount();
          break;

        case 'CAPTURE_STATUS_UPDATE':
          this.isRecording = message.isCapturing;
          this.updateRecordingStatus();
          break;
      }
    });
  }

  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');

    this.currentTab = tabName;
  }

  async toggleRecording() {
    try {
      this.isRecording = !this.isRecording;

      await this.sendMessage({
        type: 'TOGGLE_CAPTURE',
        isCapturing: this.isRecording
      });

      this.updateRecordingStatus();

      // Show notification
      this.showNotification(
        this.isRecording ? 'Recording started' : 'Recording stopped',
        this.isRecording ? 'success' : 'info'
      );
    } catch (error) {
      console.error('Error toggling recording:', error);
      this.showNotification('Error toggling recording', 'error');
    }
  }

  updateRecordingStatus() {
    const toggleBtn = document.getElementById('toggle-recording');
    const statusIndicator = document.getElementById('status-indicator');
    const statusText = statusIndicator?.querySelector('.status-text');

    if (toggleBtn) {
      const span = toggleBtn.querySelector('span');
      if (span) {
        span.textContent = this.isRecording ? 'Stop Recording' : 'Start Recording';
      }

      if (this.isRecording) {
        toggleBtn.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
      } else {
        toggleBtn.style.background = 'linear-gradient(135deg, #3498db, #2980b9)';
      }
    }

    if (statusText) {
      statusText.textContent = this.isRecording ? 'Recording' : 'Ready';
    }

    // Update body class for styling
    document.body.classList.toggle('recording', this.isRecording);
  }

  updateUI() {
    this.updateRecordingStatus();
    this.updateWorkflowsList();
    this.updateNotesList();
    this.updateWorkflowCount();
    this.updateNotesCount();
    this.updateSettings();
  }

  updateWorkflowsList() {
    const workflowList = document.getElementById('workflow-list');
    if (!workflowList) return;

    if (this.workflows.length === 0) {
      workflowList.innerHTML = `
        <div class="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <h4>No workflows yet</h4>
          <p>Start recording to create your first workflow</p>
        </div>
      `;
      return;
    }

    workflowList.innerHTML = this.workflows.map(workflow => `
      <div class="workflow-item" data-workflow-id="${workflow.id}">
        <div class="workflow-title">${this.truncateText(workflow.title, 50)}</div>
        <div class="workflow-meta">
          <span>${this.formatDate(workflow.startTime)}</span>
          <span class="workflow-steps">${workflow.steps?.length || 0} steps</span>
        </div>
      </div>
    `).join('');

    // Add click listeners to workflow items
    workflowList.querySelectorAll('.workflow-item').forEach(item => {
      item.addEventListener('click', () => {
        const workflowId = item.dataset.workflowId;
        this.openWorkflow(workflowId);
      });
    });
  }

  updateNotesList() {
    const notesList = document.getElementById('notes-list');
    if (!notesList) return;

    if (this.notes.length === 0) {
      notesList.innerHTML = `
        <div class="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
            <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
          </svg>
          <h4>No notes yet</h4>
          <p>Select text on any page to capture notes</p>
        </div>
      `;
      return;
    }

    notesList.innerHTML = this.notes.map(note => `
      <div class="note-item" data-note-id="${note.id}">
        <div class="note-text">${this.truncateText(note.text, 80)}</div>
        <div class="note-meta">
          <span>${this.formatDate(note.timestamp)}</span>
          <a href="${note.url}" target="_blank" title="${note.title}">${this.truncateText(note.title || 'Source', 20)}</a>
        </div>
      </div>
    `).join('');
  }

  updateWorkflowCount() {
    const countElement = document.getElementById('workflow-count');
    if (countElement) {
      countElement.textContent = this.workflows.length;
    }
  }

  updateNotesCount() {
    const countElement = document.getElementById('notes-count');
    if (countElement) {
      countElement.textContent = this.notes.length;
    }
  }

  updateSettings() {
    Object.keys(this.settings).forEach(key => {
      const checkbox = document.getElementById(key.replace(/([A-Z])/g, '-$1').toLowerCase());
      if (checkbox) {
        checkbox.checked = this.settings[key];
      }
    });
  }

  async searchWorkflows(query) {
    try {
      const response = await this.sendMessage({
        type: 'SEARCH_WORKFLOWS',
        query: query.trim()
      });

      this.workflows = response.results || [];
      this.updateWorkflowsList();
    } catch (error) {
      console.error('Error searching workflows:', error);
    }
  }

  searchNotes(query) {
    if (!query.trim()) {
      this.updateNotesList();
      return;
    }

    const filtered = this.notes.filter(note =>
      note.text.toLowerCase().includes(query.toLowerCase()) ||
      (note.title && note.title.toLowerCase().includes(query.toLowerCase()))
    );

    const notesList = document.getElementById('notes-list');
    if (!notesList) return;

    if (filtered.length === 0) {
      notesList.innerHTML = `
        <div class="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <circle cx="11" cy="11" r="8"/>
            <path d="M21 21l-4.35-4.35"/>
          </svg>
          <h4>No matching notes</h4>
          <p>Try a different search term</p>
        </div>
      `;
      return;
    }

    notesList.innerHTML = filtered.map(note => `
      <div class="note-item" data-note-id="${note.id}">
        <div class="note-text">${this.truncateText(note.text, 80)}</div>
        <div class="note-meta">
          <span>${this.formatDate(note.timestamp)}</span>
          <a href="${note.url}" target="_blank" title="${note.title}">${this.truncateText(note.title || 'Source', 20)}</a>
        </div>
      </div>
    `).join('');
  }

  async clearNotes() {
    if (!confirm('Are you sure you want to clear all notes? This action cannot be undone.')) {
      return;
    }

    try {
      await this.sendMessage({ type: 'CLEAR_NOTES' });
      this.notes = [];
      this.updateNotesList();
      this.updateNotesCount();
      this.showNotification('All notes cleared', 'success');
    } catch (error) {
      console.error('Error clearing notes:', error);
      this.showNotification('Error clearing notes', 'error');
    }
  }

  async updateSetting(settingKey, value) {
    // Convert kebab-case to camelCase
    const camelKey = settingKey.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
    this.settings[camelKey] = value;

    try {
      await chrome.storage.local.set({ settings: this.settings });
      this.showNotification('Settings updated', 'success');
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showNotification('Error saving settings', 'error');
    }
  }

  async openWorkflow(workflowId) {
    const workflow = this.workflows.find(w => w.id === workflowId);
    if (!workflow) return;

    try {
      // Get current active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // Send playback message to content script
      await chrome.tabs.sendMessage(tab.id, {
        type: 'START_PLAYBACK',
        workflow: workflow
      });

      // Close popup
      window.close();
    } catch (error) {
      console.error('Error opening workflow:', error);
      this.showNotification('Error opening workflow', 'error');
    }
  }

  importWorkflow() {
    // Create file input for importing workflows
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (!file) return;

      try {
        const text = await file.text();
        const workflow = JSON.parse(text);

        // Validate workflow structure
        if (!workflow.id || !workflow.title || !workflow.steps) {
          throw new Error('Invalid workflow format');
        }

        // Save imported workflow
        await this.sendMessage({
          type: 'SAVE_WORKFLOW',
          workflow: workflow
        });

        this.workflows.unshift(workflow);
        this.updateWorkflowsList();
        this.updateWorkflowCount();
        this.showNotification('Workflow imported successfully', 'success');
      } catch (error) {
        console.error('Error importing workflow:', error);
        this.showNotification('Error importing workflow', 'error');
      }
    };

    input.click();
  }

  async exportWorkflows() {
    if (this.workflows.length === 0) {
      this.showNotification('No workflows to export', 'info');
      return;
    }

    try {
      const dataStr = JSON.stringify(this.workflows, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `workflows-${new Date().toISOString().split('T')[0]}.json`;
      link.click();

      URL.revokeObjectURL(url);
      this.showNotification('Workflows exported successfully', 'success');
    } catch (error) {
      console.error('Error exporting workflows:', error);
      this.showNotification('Error exporting workflows', 'error');
    }
  }

  async exportNotes() {
    if (this.notes.length === 0) {
      this.showNotification('No notes to export', 'info');
      return;
    }

    try {
      const dataStr = JSON.stringify(this.notes, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `notes-${new Date().toISOString().split('T')[0]}.json`;
      link.click();

      URL.revokeObjectURL(url);
      this.showNotification('Notes exported successfully', 'success');
    } catch (error) {
      console.error('Error exporting notes:', error);
      this.showNotification('Error exporting notes', 'error');
    }
  }

  openHelp() {
    chrome.tabs.create({
      url: 'https://github.com/your-repo/workflowcapture-pro/wiki'
    });
  }

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    }, 10);

    // Auto-remove
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  formatDate(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;

    return date.toLocaleDateString();
  }

  async sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }
}

// Initialize the popup
const popup = new WorkflowCapturePopup();
