// Popup script for My Own Scribe extension
const toggleCaptureBtn = document.getElementById('toggle-capture');
const clearNotesBtn = document.getElementById('clear-notes');
const notesList = document.getElementById('notes-list');
const searchInput = document.getElementById('search-input');
let isCapturing = false;
let allNotes = [];

// Initialize popup state
chrome.runtime.sendMessage({ type: 'GET_CAPTURE_STATUS' }, (response) => {
  isCapturing = response.isCapturing;
  updateCaptureButton();
  loadNotes();
});

// Listen for new notes from background
chrome.runtime.onMessage.addListener((message) => {
  if (message.type === 'NEW_NOTE') {
    addNoteToUI(message.note);
  }
});

// Toggle capture state
toggleCaptureBtn.addEventListener('click', () => {
  isCapturing = !isCapturing;
  chrome.runtime.sendMessage({
    type: 'TOGGLE_CAPTURE',
    isCapturing
  });
  updateCaptureButton();
});

// Clear all notes
clearNotesBtn.addEventListener('click', () => {
  if (confirm('Are you sure you want to clear all notes?')) {
    chrome.runtime.sendMessage({ type: 'CLEAR_NOTES' }, () => {
      notesList.innerHTML = '<p class="empty-state">No notes yet. Start capturing to save selections.</p>';
    });
  }
});

// Update capture button text and color
function updateCaptureButton() {
  toggleCaptureBtn.textContent = isCapturing ? 'Stop Capture' : 'Start Capture';
  toggleCaptureBtn.style.backgroundColor = isCapturing ? '#e74c3c' : '#3498db';
}

// Load saved notes from storage
function loadNotes() {
  chrome.runtime.sendMessage({ type: 'GET_NOTES' }, (response) => {
    allNotes = response.notes;
    if (allNotes.length === 0) return;
    
    notesList.innerHTML = '';
    allNotes.forEach(note => {
      addNoteToUI(note);
    });
  });
}

// Filter notes based on search query
function filterNotes(query) {
  const filtered = allNotes.filter(note => 
    note.text.toLowerCase().includes(query.toLowerCase()) || 
    (note.context && note.context.toLowerCase().includes(query.toLowerCase()))
  );
  
  notesList.innerHTML = '';
  if (filtered.length === 0) {
    notesList.innerHTML = '<p class="empty-state">No matching notes found.</p>';
    return;
  }
  
  filtered.forEach(note => addNoteToUI(note));
}

// Search input event listener
searchInput.addEventListener('input', (e) => {
  const query = e.target.value.trim();
  if (query === '') {
    loadNotes();
  } else {
    filterNotes(query);
  }
});

// Add a note to the UI
function addNoteToUI(note) {
  // Remove empty state if present
  const emptyState = notesList.querySelector('.empty-state');
  if (emptyState) {
    notesList.removeChild(emptyState);
  }

  const noteElement = document.createElement('div');
  noteElement.className = 'note';
  noteElement.innerHTML = `
    <p><strong>${note.text}</strong></p>
    ${note.context ? `<p>${note.context}</p>` : ''}
    <small>
      ${new Date(note.timestamp).toLocaleString()} · 
      <a href="${note.url}" target="_blank">${note.title || 'Source'}</a>
    </small>
  `;
  
  // Add new note at the top
  notesList.insertBefore(noteElement, notesList.firstChild);
}
