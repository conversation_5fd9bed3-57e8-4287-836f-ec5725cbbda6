/**
 * WorkflowCapture Pro - Popup Script
 * Modern popup interface with tabbed navigation and advanced features
 */

class WorkflowCapturePopup {
  constructor() {
    this.isRecording = false;
    this.workflows = [];
    this.notes = [];
    this.screenshots = [];
    this.currentTab = 'workflows';
    this.settings = {
      autoScreenshot: true,
      smartDescriptions: true,
      highlightElements: true
    };

    this.init();
  }

  async init() {
    // Load initial data
    await this.loadData();

    // Set up event listeners
    this.setupEventListeners();

    // Initialize UI
    this.updateUI();

    // Listen for background messages
    this.setupMessageListeners();

    console.log('WorkflowCapture Pro popup initialized');
  }

  async loadData() {
    try {
      // Get capture status
      const statusResponse = await this.sendMessage({ type: 'GET_CAPTURE_STATUS' });
      this.isRecording = statusResponse?.isCapturing || false;

      // Get workflows
      await this.loadWorkflows();

      // Get notes
      const notesResponse = await this.sendMessage({ type: 'GET_NOTES' });
      this.notes = notesResponse?.notes || [];

      // Load settings from storage
      const settings = await chrome.storage.local.get(['settings']);
      if (settings.settings) {
        this.settings = { ...this.settings, ...settings.settings };
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
  }

  async loadWorkflows() {
    try {
      const workflowsResponse = await this.sendMessage({ type: 'GET_WORKFLOWS' });
      this.workflows = workflowsResponse?.workflows || [];
      this.updateWorkflowsList();
      this.updateWorkflowCount();
    } catch (error) {
      console.error('Error loading workflows:', error);
    }
  }

  setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tab = e.currentTarget.dataset.tab;
        this.switchTab(tab);
      });
    });

    // Recording toggle
    const toggleBtn = document.getElementById('toggle-recording');
    toggleBtn?.addEventListener('click', () => {
      this.toggleRecording();
    });

    // Manual screenshot capture
    const captureBtn = document.getElementById('capture-screenshot');
    captureBtn?.addEventListener('click', () => {
      this.captureManualScreenshot();
    });

    // Take screenshot button
    const takeScreenshotBtn = document.getElementById('take-screenshot');
    takeScreenshotBtn?.addEventListener('click', () => {
      this.captureManualScreenshot();
    });

    // Import workflow
    const importBtn = document.getElementById('import-workflow');
    importBtn?.addEventListener('click', () => {
      this.importWorkflow();
    });

    // Search workflows
    const workflowSearch = document.getElementById('workflow-search');
    workflowSearch?.addEventListener('input', (e) => {
      this.searchWorkflows(e.target.value);
    });

    // Search notes
    const notesSearch = document.getElementById('notes-search');
    notesSearch?.addEventListener('input', (e) => {
      this.searchNotes(e.target.value);
    });

    // Clear notes
    const clearNotesBtn = document.getElementById('clear-notes');
    clearNotesBtn?.addEventListener('click', () => {
      this.clearNotes();
    });

    // Settings checkboxes
    document.querySelectorAll('#settings-tab input[type="checkbox"]').forEach(checkbox => {
      checkbox.addEventListener('change', (e) => {
        this.updateSetting(e.target.id, e.target.checked);
      });
    });

    // Export buttons
    const exportWorkflowsBtn = document.getElementById('export-workflows');
    exportWorkflowsBtn?.addEventListener('click', () => {
      this.exportWorkflows();
    });

    const exportNotesBtn = document.getElementById('export-notes');
    exportNotesBtn?.addEventListener('click', () => {
      this.exportNotes();
    });

    // Help link
    const helpLink = document.getElementById('help-link');
    helpLink?.addEventListener('click', (e) => {
      e.preventDefault();
      this.openHelp();
    });
  }

  setupMessageListeners() {
    chrome.runtime.onMessage.addListener((message) => {
      switch (message.type) {
        case 'NEW_WORKFLOW':
          this.workflows.unshift(message.workflow);
          this.updateWorkflowsList();
          this.updateWorkflowCount();
          break;

        case 'NEW_NOTE':
          this.notes.unshift(message.note);
          this.updateNotesList();
          this.updateNotesCount();
          break;

        case 'CAPTURE_STATUS_UPDATE':
          this.isRecording = message.isCapturing;
          this.updateRecordingStatus();
          break;
      }
    });
  }

  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');

    this.currentTab = tabName;
  }

  async toggleRecording() {
    try {
      if (!this.isRecording) {
        // Start recording
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        await this.sendMessage({
          type: 'START_RECORDING',
          tab: tab
        });
        this.isRecording = true;
      } else {
        // Stop recording
        await this.sendMessage({
          type: 'STOP_RECORDING'
        });
        this.isRecording = false;

        // Reload workflows to show the new one
        await this.loadWorkflows();
      }

      this.updateRecordingStatus();

      // Show notification
      this.showNotification(
        this.isRecording ? 'Recording started' : 'Recording stopped',
        this.isRecording ? 'success' : 'info'
      );
    } catch (error) {
      console.error('Error toggling recording:', error);
      this.showNotification('Error toggling recording', 'error');
    }
  }

  updateRecordingStatus() {
    const toggleBtn = document.getElementById('toggle-recording');
    const statusIndicator = document.getElementById('status-indicator');
    const statusText = statusIndicator?.querySelector('.status-text');

    if (toggleBtn) {
      const span = toggleBtn.querySelector('span');
      if (span) {
        span.textContent = this.isRecording ? 'Stop Recording' : 'Start Recording';
      }

      if (this.isRecording) {
        toggleBtn.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
      } else {
        toggleBtn.style.background = 'linear-gradient(135deg, #3498db, #2980b9)';
      }
    }

    if (statusText) {
      statusText.textContent = this.isRecording ? 'Recording' : 'Ready';
    }

    // Update body class for styling
    document.body.classList.toggle('recording', this.isRecording);
  }

  updateUI() {
    this.updateRecordingStatus();
    this.updateWorkflowsList();
    this.updateNotesList();
    this.updateScreenshotsList();
    this.updateWorkflowCount();
    this.updateNotesCount();
    this.updateScreenshotsCount();
    this.updateSettings();
  }

  updateWorkflowsList() {
    const workflowList = document.getElementById('workflow-list');
    if (!workflowList) return;

    if (this.workflows.length === 0) {
      workflowList.innerHTML = `
        <div class="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <h4>No workflows yet</h4>
          <p>Start recording to create your first workflow</p>
        </div>
      `;
      return;
    }

    workflowList.innerHTML = this.workflows.map(workflow => `
      <div class="workflow-item" data-workflow-id="${workflow.id}">
        <div class="workflow-title">${this.truncateText(workflow.title, 50)}</div>
        <div class="workflow-meta">
          <span>${this.formatDate(workflow.startTime)}</span>
          <span class="workflow-steps">${workflow.steps?.length || 0} steps</span>
        </div>
      </div>
    `).join('');

    // Add click listeners to workflow items
    workflowList.querySelectorAll('.workflow-item').forEach(item => {
      item.addEventListener('click', () => {
        const workflowId = item.dataset.workflowId;
        this.openWorkflow(workflowId);
      });
    });
  }

  updateNotesList() {
    const notesList = document.getElementById('notes-list');
    if (!notesList) return;

    if (this.notes.length === 0) {
      notesList.innerHTML = `
        <div class="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
            <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
          </svg>
          <h4>No notes yet</h4>
          <p>Select text on any page to capture notes</p>
        </div>
      `;
      return;
    }

    notesList.innerHTML = this.notes.map(note => `
      <div class="note-item" data-note-id="${note.id}">
        <div class="note-text">${this.truncateText(note.text, 80)}</div>
        <div class="note-meta">
          <span>${this.formatDate(note.timestamp)}</span>
          <a href="${note.url}" target="_blank" title="${note.title}">${this.truncateText(note.title || 'Source', 20)}</a>
        </div>
      </div>
    `).join('');
  }

  updateWorkflowCount() {
    const countElement = document.getElementById('workflow-count');
    if (countElement) {
      countElement.textContent = this.workflows.length;
    }
  }

  updateNotesCount() {
    const countElement = document.getElementById('notes-count');
    if (countElement) {
      countElement.textContent = this.notes.length;
    }
  }

  updateSettings() {
    Object.keys(this.settings).forEach(key => {
      const checkbox = document.getElementById(key.replace(/([A-Z])/g, '-$1').toLowerCase());
      if (checkbox) {
        checkbox.checked = this.settings[key];
      }
    });
  }

  async searchWorkflows(query) {
    try {
      const response = await this.sendMessage({
        type: 'SEARCH_WORKFLOWS',
        query: query.trim()
      });

      this.workflows = response.results || [];
      this.updateWorkflowsList();
    } catch (error) {
      console.error('Error searching workflows:', error);
    }
  }

  searchNotes(query) {
    if (!query.trim()) {
      this.updateNotesList();
      return;
    }

    const filtered = this.notes.filter(note =>
      note.text.toLowerCase().includes(query.toLowerCase()) ||
      (note.title && note.title.toLowerCase().includes(query.toLowerCase()))
    );

    const notesList = document.getElementById('notes-list');
    if (!notesList) return;

    if (filtered.length === 0) {
      notesList.innerHTML = `
        <div class="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <circle cx="11" cy="11" r="8"/>
            <path d="M21 21l-4.35-4.35"/>
          </svg>
          <h4>No matching notes</h4>
          <p>Try a different search term</p>
        </div>
      `;
      return;
    }

    notesList.innerHTML = filtered.map(note => `
      <div class="note-item" data-note-id="${note.id}">
        <div class="note-text">${this.truncateText(note.text, 80)}</div>
        <div class="note-meta">
          <span>${this.formatDate(note.timestamp)}</span>
          <a href="${note.url}" target="_blank" title="${note.title}">${this.truncateText(note.title || 'Source', 20)}</a>
        </div>
      </div>
    `).join('');
  }

  async clearNotes() {
    if (!confirm('Are you sure you want to clear all notes? This action cannot be undone.')) {
      return;
    }

    try {
      await this.sendMessage({ type: 'CLEAR_NOTES' });
      this.notes = [];
      this.updateNotesList();
      this.updateNotesCount();
      this.showNotification('All notes cleared', 'success');
    } catch (error) {
      console.error('Error clearing notes:', error);
      this.showNotification('Error clearing notes', 'error');
    }
  }

  async updateSetting(settingKey, value) {
    // Convert kebab-case to camelCase
    const camelKey = settingKey.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
    this.settings[camelKey] = value;

    try {
      await chrome.storage.local.set({ settings: this.settings });
      this.showNotification('Settings updated', 'success');
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showNotification('Error saving settings', 'error');
    }
  }

  async openWorkflow(workflowId) {
    const workflow = this.workflows.find(w => w.id === workflowId);
    if (!workflow) return;

    try {
      // Get current active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // Send playback message to content script
      await chrome.tabs.sendMessage(tab.id, {
        type: 'START_PLAYBACK',
        workflow: workflow
      });

      // Close popup
      window.close();
    } catch (error) {
      console.error('Error opening workflow:', error);
      this.showNotification('Error opening workflow', 'error');
    }
  }

  importWorkflow() {
    // Create file input for importing workflows
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (!file) return;

      try {
        const text = await file.text();
        const workflow = JSON.parse(text);

        // Validate workflow structure
        if (!workflow.id || !workflow.title || !workflow.steps) {
          throw new Error('Invalid workflow format');
        }

        // Save imported workflow
        await this.sendMessage({
          type: 'SAVE_WORKFLOW',
          workflow: workflow
        });

        this.workflows.unshift(workflow);
        this.updateWorkflowsList();
        this.updateWorkflowCount();
        this.showNotification('Workflow imported successfully', 'success');
      } catch (error) {
        console.error('Error importing workflow:', error);
        this.showNotification('Error importing workflow', 'error');
      }
    };

    input.click();
  }

  async exportWorkflows() {
    if (this.workflows.length === 0) {
      this.showNotification('No workflows to export', 'info');
      return;
    }

    try {
      const dataStr = JSON.stringify(this.workflows, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `workflows-${new Date().toISOString().split('T')[0]}.json`;
      link.click();

      URL.revokeObjectURL(url);
      this.showNotification('Workflows exported successfully', 'success');
    } catch (error) {
      console.error('Error exporting workflows:', error);
      this.showNotification('Error exporting workflows', 'error');
    }
  }

  async exportNotes() {
    if (this.notes.length === 0) {
      this.showNotification('No notes to export', 'info');
      return;
    }

    try {
      const dataStr = JSON.stringify(this.notes, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `notes-${new Date().toISOString().split('T')[0]}.json`;
      link.click();

      URL.revokeObjectURL(url);
      this.showNotification('Notes exported successfully', 'success');
    } catch (error) {
      console.error('Error exporting notes:', error);
      this.showNotification('Error exporting notes', 'error');
    }
  }

  async captureManualScreenshot() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // Create a manual screenshot with click-like metadata
      const response = await this.sendMessage({
        type: 'CAPTURE_CLICK_SCREENSHOT',
        clickData: {
          coordinates: { x: 0, y: 0 },
          element: { tagName: 'manual', text: 'Manual capture' },
          timestamp: Date.now(),
          sequenceNumber: 0,
          pageUrl: tab.url,
          pageTitle: tab.title
        }
      });

      if (response.screenshot) {
        this.screenshots.unshift(response.screenshot);
        this.updateScreenshotsList();
        this.updateScreenshotsCount();
        this.showNotification('Screenshot captured successfully', 'success');

        // Switch to screenshots tab
        this.switchTab('screenshots');

        // Open screenshot editor
        this.openScreenshotEditor(response.screenshot);
      } else {
        throw new Error('Failed to capture screenshot');
      }
    } catch (error) {
      console.error('Error capturing screenshot:', error);
      this.showNotification('Error capturing screenshot', 'error');
    }
  }

  updateScreenshotsList() {
    const screenshotsList = document.getElementById('screenshots-list');
    if (!screenshotsList) return;

    if (this.screenshots.length === 0) {
      screenshotsList.innerHTML = `
        <div class="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M23 19a2 2 0 01-2 2H3a2 2 0 01-2-2V8a2 2 0 012-2h4l2-3h6l2 3h4a2 2 0 012 2z"/>
            <circle cx="12" cy="13" r="4"/>
          </svg>
          <h4>No screenshots yet</h4>
          <p>Take screenshots to capture and annotate your screen</p>
        </div>
      `;
      return;
    }

    screenshotsList.innerHTML = this.screenshots.map(screenshot => `
      <div class="screenshot-item" data-screenshot-id="${screenshot.id}">
        <div class="screenshot-preview">
          <img src="${screenshot.dataUrl}" alt="Screenshot" style="width: 100%; height: 60px; object-fit: cover; border-radius: 4px;">
        </div>
        <div class="screenshot-meta">
          <span>${this.formatDate(screenshot.timestamp)}</span>
          <div class="screenshot-actions">
            <button class="btn-icon edit-screenshot" title="Edit Screenshot">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
                <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
              </svg>
            </button>
            <button class="btn-icon download-screenshot" title="Download Screenshot">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    `).join('');

    // Add event listeners to screenshot items
    screenshotsList.querySelectorAll('.edit-screenshot').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const screenshotId = e.target.closest('.screenshot-item').dataset.screenshotId;
        const screenshot = this.screenshots.find(s => s.id === screenshotId);
        if (screenshot) {
          this.openScreenshotEditor(screenshot);
        }
      });
    });

    screenshotsList.querySelectorAll('.download-screenshot').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const screenshotId = e.target.closest('.screenshot-item').dataset.screenshotId;
        const screenshot = this.screenshots.find(s => s.id === screenshotId);
        if (screenshot) {
          this.downloadScreenshot(screenshot);
        }
      });
    });
  }

  updateScreenshotsCount() {
    const countElement = document.getElementById('screenshots-count');
    if (countElement) {
      countElement.textContent = this.screenshots.length;
    }
  }

  openScreenshotEditor(screenshot) {
    // Create screenshot editor modal
    const modal = document.createElement('div');
    modal.className = 'screenshot-editor-modal';
    modal.innerHTML = `
      <div class="screenshot-editor-overlay">
        <div class="screenshot-editor-container">
          <div class="screenshot-editor-header">
            <h3>Edit Screenshot</h3>
            <button class="close-editor" title="Close Editor">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          </div>

          <div class="screenshot-editor-toolbar">
            <button class="tool-btn active" data-tool="select" title="Select">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"/>
              </svg>
            </button>
            <button class="tool-btn" data-tool="text" title="Add Text">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="4,7 4,4 20,4 20,7"/>
                <line x1="9" y1="20" x2="15" y2="20"/>
                <line x1="12" y1="4" x2="12" y2="20"/>
              </svg>
            </button>
            <button class="tool-btn" data-tool="arrow" title="Add Arrow">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="5" y1="12" x2="19" y2="12"/>
                <polyline points="12,5 19,12 12,19"/>
              </svg>
            </button>
            <button class="tool-btn" data-tool="highlight" title="Highlight">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 11H1l6-6 6 6z"/>
                <path d="M9 17l3 3 3-3"/>
                <path d="M22 12h-8"/>
                <path d="M3 21h18"/>
              </svg>
            </button>
            <button class="tool-btn" data-tool="draw" title="Draw">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 19l7-7 3 3-7 7-3-3z"/>
                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/>
                <path d="M2 2l7.586 7.586"/>
                <circle cx="11" cy="11" r="2"/>
              </svg>
            </button>
            <div class="color-picker">
              <input type="color" id="annotation-color" value="#ff0000">
            </div>
          </div>

          <div class="screenshot-editor-canvas-container">
            <canvas id="screenshot-canvas" style="border: 1px solid #ddd; max-width: 100%; max-height: 400px;"></canvas>
          </div>

          <div class="screenshot-editor-actions">
            <button class="btn btn-outline" id="cancel-edit">Cancel</button>
            <button class="btn btn-primary" id="save-edit">Save Changes</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Initialize canvas
    this.initializeScreenshotEditor(screenshot, modal);
  }

  initializeScreenshotEditor(screenshot, modal) {
    const canvas = modal.querySelector('#screenshot-canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Set canvas size to image size
      canvas.width = img.width;
      canvas.height = img.height;

      // Draw the screenshot
      ctx.drawImage(img, 0, 0);

      // Set up drawing state
      let currentTool = 'select';
      let isDrawing = false;
      let startX, startY;
      // Removed unused annotations array

      // Tool selection
      modal.querySelectorAll('.tool-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          modal.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
          btn.classList.add('active');
          currentTool = btn.dataset.tool;
        });
      });

      // Canvas drawing events
      canvas.addEventListener('mousedown', (e) => {
        const rect = canvas.getBoundingClientRect();
        startX = (e.clientX - rect.left) * (canvas.width / rect.width);
        startY = (e.clientY - rect.top) * (canvas.height / rect.height);
        isDrawing = true;

        if (currentTool === 'text') {
          this.addTextAnnotation(ctx, startX, startY, modal);
        }
      });

      canvas.addEventListener('mousemove', (e) => {
        if (!isDrawing) return;

        const rect = canvas.getBoundingClientRect();
        const currentX = (e.clientX - rect.left) * (canvas.width / rect.width);
        const currentY = (e.clientY - rect.top) * (canvas.height / rect.height);

        if (currentTool === 'draw') {
          ctx.strokeStyle = modal.querySelector('#annotation-color').value;
          ctx.lineWidth = 3;
          ctx.lineCap = 'round';
          ctx.lineTo(currentX, currentY);
          ctx.stroke();
          ctx.beginPath();
          ctx.moveTo(currentX, currentY);
        }
      });

      canvas.addEventListener('mouseup', (e) => {
        if (!isDrawing) return;
        isDrawing = false;

        const rect = canvas.getBoundingClientRect();
        const endX = (e.clientX - rect.left) * (canvas.width / rect.width);
        const endY = (e.clientY - rect.top) * (canvas.height / rect.height);

        const color = modal.querySelector('#annotation-color').value;

        if (currentTool === 'arrow') {
          this.drawArrow(ctx, startX, startY, endX, endY, color);
        } else if (currentTool === 'highlight') {
          this.drawHighlight(ctx, startX, startY, endX, endY);
        }

        ctx.beginPath();
      });

      // Start drawing path for draw tool
      canvas.addEventListener('mousedown', (e) => {
        if (currentTool === 'draw') {
          const rect = canvas.getBoundingClientRect();
          const x = (e.clientX - rect.left) * (canvas.width / rect.width);
          const y = (e.clientY - rect.top) * (canvas.height / rect.height);
          ctx.beginPath();
          ctx.moveTo(x, y);
        }
      });
    };

    img.src = screenshot.dataUrl;

    // Modal event listeners
    modal.querySelector('.close-editor').addEventListener('click', () => {
      modal.remove();
    });

    modal.querySelector('#cancel-edit').addEventListener('click', () => {
      modal.remove();
    });

    modal.querySelector('#save-edit').addEventListener('click', () => {
      // Save the edited screenshot
      const editedDataUrl = canvas.toDataURL('image/png');
      screenshot.dataUrl = editedDataUrl;
      screenshot.edited = true;
      screenshot.editedAt = Date.now();

      this.updateScreenshotsList();
      this.showNotification('Screenshot saved successfully', 'success');
      modal.remove();
    });

    // Close modal when clicking overlay
    modal.querySelector('.screenshot-editor-overlay').addEventListener('click', (e) => {
      if (e.target === e.currentTarget) {
        modal.remove();
      }
    });
  }

  addTextAnnotation(ctx, x, y, modal) {
    const text = prompt('Enter text annotation:');
    if (text) {
      const color = modal.querySelector('#annotation-color').value;
      ctx.fillStyle = color;
      ctx.font = '16px Arial';
      ctx.fillText(text, x, y);
    }
  }

  drawArrow(ctx, startX, startY, endX, endY, color) {
    ctx.strokeStyle = color;
    ctx.fillStyle = color;
    ctx.lineWidth = 2;

    // Draw line
    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.stroke();

    // Draw arrowhead
    const angle = Math.atan2(endY - startY, endX - startX);
    const arrowLength = 15;

    ctx.beginPath();
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - arrowLength * Math.cos(angle - Math.PI / 6),
      endY - arrowLength * Math.sin(angle - Math.PI / 6)
    );
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - arrowLength * Math.cos(angle + Math.PI / 6),
      endY - arrowLength * Math.sin(angle + Math.PI / 6)
    );
    ctx.stroke();
  }

  drawHighlight(ctx, startX, startY, endX, endY) {
    ctx.fillStyle = 'rgba(255, 255, 0, 0.3)';
    ctx.fillRect(
      Math.min(startX, endX),
      Math.min(startY, endY),
      Math.abs(endX - startX),
      Math.abs(endY - startY)
    );
  }

  downloadScreenshot(screenshot) {
    const link = document.createElement('a');
    link.href = screenshot.dataUrl;
    link.download = `screenshot-${new Date(screenshot.timestamp).toISOString().split('T')[0]}.png`;
    link.click();
    this.showNotification('Screenshot downloaded', 'success');
  }

  openHelp() {
    chrome.tabs.create({
      url: 'https://github.com/your-repo/workflowcapture-pro/wiki'
    });
  }

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    }, 10);

    // Auto-remove
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  formatDate(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;

    return date.toLocaleDateString();
  }

  async sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }
}

// Initialize the popup
const popup = new WorkflowCapturePopup();
