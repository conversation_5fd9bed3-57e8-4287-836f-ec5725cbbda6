/**
 * WorkflowCapture Pro - Screenshot Review Page
 * Advanced screenshot management and editing interface
 */

class ScreenshotReviewManager {
  constructor() {
    this.screenshots = [];
    this.currentScreenshot = null;
    this.sessionId = null;
    this.sessionTimestamp = null;
    this.advancedEditor = null;
    
    this.init();
  }

  async init() {
    try {
      // Load session data
      await this.loadSessionData();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Render the interface
      this.renderInterface();
      
      console.log('Screenshot Review Manager initialized');
    } catch (error) {
      console.error('Failed to initialize Screenshot Review Manager:', error);
      this.showError('Failed to load screenshot session');
    }
  }

  async loadSessionData() {
    try {
      const data = await chrome.storage.local.get([
        'currentSessionScreenshots',
        'sessionId',
        'sessionTimestamp'
      ]);
      
      this.screenshots = data.currentSessionScreenshots || [];
      this.sessionId = data.sessionId;
      this.sessionTimestamp = data.sessionTimestamp;
      
      // Load any existing comments
      const commentsData = await chrome.storage.local.get(['screenshotComments']);
      const comments = commentsData.screenshotComments || {};
      
      // Merge comments with screenshots
      this.screenshots.forEach(screenshot => {
        if (comments[screenshot.id]) {
          screenshot.comments = comments[screenshot.id];
        }
      });
      
    } catch (error) {
      console.error('Error loading session data:', error);
      throw error;
    }
  }

  setupEventListeners() {
    // Export PDF button
    document.getElementById('export-pdf-btn')?.addEventListener('click', () => {
      this.exportToPDF();
    });

    // Edit all button
    document.getElementById('edit-all-btn')?.addEventListener('click', () => {
      this.editAllScreenshots();
    });

    // Screenshot details actions
    document.getElementById('edit-screenshot-btn')?.addEventListener('click', () => {
      if (this.currentScreenshot) {
        this.openAdvancedEditor(this.currentScreenshot);
      }
    });

    document.getElementById('download-screenshot-btn')?.addEventListener('click', () => {
      if (this.currentScreenshot) {
        this.downloadScreenshot(this.currentScreenshot);
      }
    });

    // Save comment button
    document.getElementById('save-comment-btn')?.addEventListener('click', () => {
      this.saveCurrentComment();
    });

    // Advanced editor events
    document.getElementById('close-advanced-editor')?.addEventListener('click', () => {
      this.closeAdvancedEditor();
    });

    document.getElementById('cancel-advanced-edit')?.addEventListener('click', () => {
      this.closeAdvancedEditor();
    });

    document.getElementById('save-advanced-edit')?.addEventListener('click', () => {
      this.saveAdvancedEdit();
    });
  }

  renderInterface() {
    this.updateSessionInfo();
    this.renderThumbnails();
    this.updateScreenshotCount();
  }

  updateSessionInfo() {
    const sessionInfo = document.getElementById('session-info');
    if (sessionInfo && this.sessionTimestamp) {
      const date = new Date(this.sessionTimestamp);
      sessionInfo.textContent = `Session from ${date.toLocaleDateString()} at ${date.toLocaleTimeString()} • ${this.screenshots.length} screenshots`;
    }
  }

  renderThumbnails() {
    const thumbnailGrid = document.getElementById('thumbnail-grid');
    if (!thumbnailGrid) return;

    if (this.screenshots.length === 0) {
      thumbnailGrid.innerHTML = `
        <div class="empty-state" style="grid-column: 1 / -1; text-align: center; padding: 40px 20px;">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M23 19a2 2 0 01-2 2H3a2 2 0 01-2-2V8a2 2 0 012-2h4l2-3h6l2 3h4a2 2 0 012 2z"/>
            <circle cx="12" cy="13" r="4"/>
          </svg>
          <h4>No screenshots found</h4>
          <p>No screenshots were captured in this session</p>
        </div>
      `;
      return;
    }

    thumbnailGrid.innerHTML = this.screenshots.map((screenshot, index) => `
      <div class="thumbnail-item" data-screenshot-id="${screenshot.id}">
        <img src="${screenshot.dataUrl}" alt="Screenshot ${index + 1}" class="thumbnail-image">
        <div class="thumbnail-info">
          <div class="thumbnail-sequence">Step ${screenshot.sequenceNumber}</div>
          <div class="thumbnail-time">${this.formatTime(screenshot.timestamp)}</div>
        </div>
      </div>
    `).join('');

    // Add click listeners to thumbnails
    thumbnailGrid.querySelectorAll('.thumbnail-item').forEach(item => {
      item.addEventListener('click', () => {
        const screenshotId = item.dataset.screenshotId;
        const screenshot = this.screenshots.find(s => s.id === screenshotId);
        if (screenshot) {
          this.selectScreenshot(screenshot);
        }
      });
    });
  }

  selectScreenshot(screenshot) {
    this.currentScreenshot = screenshot;
    
    // Update active thumbnail
    document.querySelectorAll('.thumbnail-item').forEach(item => {
      item.classList.remove('active');
    });
    document.querySelector(`[data-screenshot-id="${screenshot.id}"]`)?.classList.add('active');
    
    // Display screenshot
    this.displayScreenshot(screenshot);
    
    // Show details panel
    this.showScreenshotDetails(screenshot);
  }

  displayScreenshot(screenshot) {
    const viewer = document.getElementById('screenshot-viewer');
    if (!viewer) return;

    viewer.innerHTML = `
      <img src="${screenshot.dataUrl}" alt="Screenshot" class="screenshot-display" id="main-screenshot">
    `;

    // Add click listener to open in advanced editor
    const img = viewer.querySelector('#main-screenshot');
    img?.addEventListener('click', () => {
      this.openAdvancedEditor(screenshot);
    });
  }

  showScreenshotDetails(screenshot) {
    const detailsPanel = document.getElementById('screenshot-details');
    if (!detailsPanel) return;

    detailsPanel.style.display = 'block';

    // Update detail fields
    document.getElementById('detail-sequence').textContent = `Step ${screenshot.sequenceNumber}`;
    document.getElementById('detail-timestamp').textContent = new Date(screenshot.timestamp).toLocaleString();
    document.getElementById('detail-url').textContent = screenshot.pageUrl;
    
    const elementText = screenshot.element ? 
      `${screenshot.element.tagName}${screenshot.element.id ? '#' + screenshot.element.id : ''}${screenshot.element.className ? '.' + screenshot.element.className.split(' ')[0] : ''}` :
      'Unknown element';
    document.getElementById('detail-element').textContent = elementText;

    // Load existing comment
    const commentTextarea = document.getElementById('screenshot-comment');
    if (commentTextarea) {
      commentTextarea.value = screenshot.comments || '';
    }
  }

  async saveCurrentComment() {
    if (!this.currentScreenshot) return;

    const commentTextarea = document.getElementById('screenshot-comment');
    const comment = commentTextarea?.value || '';

    // Update screenshot object
    this.currentScreenshot.comments = comment;

    // Save to storage
    try {
      const commentsData = await chrome.storage.local.get(['screenshotComments']);
      const comments = commentsData.screenshotComments || {};
      comments[this.currentScreenshot.id] = comment;
      
      await chrome.storage.local.set({ screenshotComments: comments });
      
      this.showNotification('Comment saved successfully', 'success');
    } catch (error) {
      console.error('Error saving comment:', error);
      this.showNotification('Error saving comment', 'error');
    }
  }

  updateScreenshotCount() {
    const countElement = document.getElementById('screenshot-count');
    if (countElement) {
      countElement.textContent = this.screenshots.length;
    }
  }

  formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  openAdvancedEditor(screenshot) {
    const modal = document.getElementById('advanced-editor-modal');
    if (!modal) return;

    modal.style.display = 'flex';
    
    // Initialize the advanced editor
    this.initializeAdvancedEditor(screenshot);
  }

  closeAdvancedEditor() {
    const modal = document.getElementById('advanced-editor-modal');
    if (modal) {
      modal.style.display = 'none';
    }
    
    if (this.advancedEditor) {
      this.advancedEditor.destroy();
      this.advancedEditor = null;
    }
  }

  initializeAdvancedEditor(screenshot) {
    const canvas = document.getElementById('advanced-editor-canvas');
    if (!canvas) return;

    this.advancedEditor = new AdvancedScreenshotEditor(canvas, screenshot);
    this.setupAdvancedEditorTools();
  }

  setupAdvancedEditorTools() {
    // Tool selection
    document.querySelectorAll('.tool-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        
        if (this.advancedEditor) {
          this.advancedEditor.setTool(btn.dataset.tool);
        }
      });
    });

    // Tool options
    document.getElementById('tool-color')?.addEventListener('change', (e) => {
      if (this.advancedEditor) {
        this.advancedEditor.setColor(e.target.value);
      }
    });

    document.getElementById('tool-size')?.addEventListener('input', (e) => {
      if (this.advancedEditor) {
        this.advancedEditor.setSize(parseInt(e.target.value));
      }
      document.getElementById('size-value').textContent = e.target.value;
    });

    document.getElementById('tool-font')?.addEventListener('change', (e) => {
      if (this.advancedEditor) {
        this.advancedEditor.setFont(e.target.value);
      }
    });

    // Layer controls
    document.getElementById('undo-btn')?.addEventListener('click', () => {
      if (this.advancedEditor) {
        this.advancedEditor.undo();
      }
    });

    document.getElementById('redo-btn')?.addEventListener('click', () => {
      if (this.advancedEditor) {
        this.advancedEditor.redo();
      }
    });

    document.getElementById('clear-all-btn')?.addEventListener('click', () => {
      if (this.advancedEditor && confirm('Clear all annotations?')) {
        this.advancedEditor.clearAll();
      }
    });
  }

  saveAdvancedEdit() {
    if (!this.advancedEditor || !this.currentScreenshot) return;

    // Get the edited image data
    const editedDataUrl = this.advancedEditor.getImageData();
    
    // Update the screenshot
    this.currentScreenshot.dataUrl = editedDataUrl;
    this.currentScreenshot.edited = true;
    this.currentScreenshot.editedAt = Date.now();

    // Update the display
    this.displayScreenshot(this.currentScreenshot);
    this.renderThumbnails();

    // Close the editor
    this.closeAdvancedEditor();

    this.showNotification('Screenshot updated successfully', 'success');
  }

  downloadScreenshot(screenshot) {
    const link = document.createElement('a');
    link.href = screenshot.dataUrl;
    link.download = `screenshot-step-${screenshot.sequenceNumber}-${new Date(screenshot.timestamp).toISOString().split('T')[0]}.png`;
    link.click();
    
    this.showNotification('Screenshot downloaded', 'success');
  }

  editAllScreenshots() {
    // Open a batch editing interface
    this.showNotification('Batch editing feature coming soon', 'info');
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    }, 10);
    
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  showError(message) {
    const viewer = document.getElementById('screenshot-viewer');
    if (viewer) {
      viewer.innerHTML = `
        <div class="empty-state">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          <h3>Error</h3>
          <p>${message}</p>
        </div>
      `;
    }
  }
}

  async exportToPDF() {
    if (this.screenshots.length === 0) {
      this.showNotification('No screenshots to export', 'error');
      return;
    }

    // Show progress overlay
    const progressOverlay = document.getElementById('progress-overlay');
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');

    if (progressOverlay) {
      progressOverlay.style.display = 'flex';
    }

    try {
      // Load jsPDF library dynamically
      await this.loadJsPDF();

      const { jsPDF } = window;
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const contentWidth = pageWidth - (margin * 2);

      // Add cover page
      this.addCoverPage(pdf, pageWidth, pageHeight, margin);

      // Add table of contents
      pdf.addPage();
      this.addTableOfContents(pdf, pageWidth, pageHeight, margin);

      // Process each screenshot
      for (let i = 0; i < this.screenshots.length; i++) {
        const screenshot = this.screenshots[i];

        // Update progress
        const progress = ((i + 1) / this.screenshots.length) * 100;
        if (progressFill) progressFill.style.width = `${progress}%`;
        if (progressText) progressText.textContent = `Processing screenshot ${i + 1} of ${this.screenshots.length}...`;

        // Add new page for each screenshot
        pdf.addPage();

        // Add screenshot
        await this.addScreenshotToPage(pdf, screenshot, pageWidth, pageHeight, margin, contentWidth, i + 1);

        // Small delay to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Generate and download PDF
      if (progressText) progressText.textContent = 'Generating PDF file...';

      const fileName = `workflow-screenshots-${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(fileName);

      this.showNotification('PDF exported successfully', 'success');

    } catch (error) {
      console.error('PDF export failed:', error);
      this.showNotification('PDF export failed', 'error');
    } finally {
      // Hide progress overlay
      if (progressOverlay) {
        progressOverlay.style.display = 'none';
      }
    }
  }

  async loadJsPDF() {
    if (window.jsPDF) return;

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  addCoverPage(pdf, pageWidth, pageHeight, margin) {
    // Title
    pdf.setFontSize(24);
    pdf.setFont(undefined, 'bold');
    pdf.text('Workflow Documentation', pageWidth / 2, 60, { align: 'center' });

    // Subtitle
    pdf.setFontSize(16);
    pdf.setFont(undefined, 'normal');
    pdf.text('Screenshot Capture Session', pageWidth / 2, 80, { align: 'center' });

    // Date and session info
    pdf.setFontSize(12);
    const date = new Date(this.sessionTimestamp);
    pdf.text(`Generated on: ${date.toLocaleDateString()} at ${date.toLocaleTimeString()}`, pageWidth / 2, 100, { align: 'center' });
    pdf.text(`Total Screenshots: ${this.screenshots.length}`, pageWidth / 2, 115, { align: 'center' });

    // Add a decorative line
    pdf.setLineWidth(0.5);
    pdf.line(margin, 130, pageWidth - margin, 130);

    // Summary
    pdf.setFontSize(14);
    pdf.setFont(undefined, 'bold');
    pdf.text('Session Summary', margin, 150);

    pdf.setFontSize(11);
    pdf.setFont(undefined, 'normal');
    const summaryText = `This document contains ${this.screenshots.length} screenshots captured during a workflow recording session. Each screenshot represents a user interaction step and includes detailed annotations and comments.`;

    const splitSummary = pdf.splitTextToSize(summaryText, contentWidth);
    pdf.text(splitSummary, margin, 165);
  }

  addTableOfContents(pdf, pageWidth, pageHeight, margin) {
    pdf.setFontSize(18);
    pdf.setFont(undefined, 'bold');
    pdf.text('Table of Contents', margin, 40);

    pdf.setFontSize(11);
    pdf.setFont(undefined, 'normal');

    let yPosition = 60;

    this.screenshots.forEach((screenshot, index) => {
      const pageNumber = index + 3; // Cover page + TOC + screenshots start at page 3
      const title = `Step ${screenshot.sequenceNumber}: ${this.getScreenshotTitle(screenshot)}`;

      pdf.text(`${title}`, margin, yPosition);
      pdf.text(`${pageNumber}`, pageWidth - margin - 10, yPosition, { align: 'right' });

      yPosition += 8;

      // Add new page if needed
      if (yPosition > pageHeight - margin) {
        pdf.addPage();
        yPosition = margin;
      }
    });
  }

  async addScreenshotToPage(pdf, screenshot, pageWidth, pageHeight, margin, contentWidth, stepNumber) {
    // Add step header
    pdf.setFontSize(16);
    pdf.setFont(undefined, 'bold');
    pdf.text(`Step ${screenshot.sequenceNumber}`, margin, 30);

    // Add timestamp and URL
    pdf.setFontSize(10);
    pdf.setFont(undefined, 'normal');
    pdf.text(`Captured: ${new Date(screenshot.timestamp).toLocaleString()}`, margin, 40);

    const urlText = pdf.splitTextToSize(`URL: ${screenshot.pageUrl}`, contentWidth);
    pdf.text(urlText, margin, 48);

    // Add screenshot image
    try {
      const imgData = screenshot.dataUrl;
      const imgProps = pdf.getImageProperties(imgData);
      const imgWidth = contentWidth;
      const imgHeight = (imgProps.height * imgWidth) / imgProps.width;

      // Ensure image fits on page
      const maxImageHeight = pageHeight - 120; // Leave space for header and comments
      const finalHeight = Math.min(imgHeight, maxImageHeight);
      const finalWidth = (imgProps.width * finalHeight) / imgProps.height;

      pdf.addImage(imgData, 'PNG', margin, 60, finalWidth, finalHeight);

      // Add comments section
      const commentsY = 60 + finalHeight + 10;

      if (commentsY < pageHeight - 40) {
        pdf.setFontSize(12);
        pdf.setFont(undefined, 'bold');
        pdf.text('Comments:', margin, commentsY);

        pdf.setFontSize(10);
        pdf.setFont(undefined, 'normal');

        const comments = screenshot.comments || 'No comments added.';
        const splitComments = pdf.splitTextToSize(comments, contentWidth);
        pdf.text(splitComments, margin, commentsY + 8);
      }

    } catch (error) {
      console.error('Error adding image to PDF:', error);
      pdf.setFontSize(12);
      pdf.text('Error: Could not load screenshot image', margin, 80);
    }

    // Add page number
    pdf.setFontSize(8);
    pdf.text(`Page ${pdf.internal.getNumberOfPages()}`, pageWidth - margin - 10, pageHeight - 10, { align: 'right' });
  }

  getScreenshotTitle(screenshot) {
    if (screenshot.element && screenshot.element.text) {
      return screenshot.element.text.substring(0, 50) + (screenshot.element.text.length > 50 ? '...' : '');
    }
    return `Click on ${screenshot.element?.tagName || 'element'}`;
  }
}

/**
 * Advanced Screenshot Editor Class
 * Provides comprehensive editing tools for screenshots
 */
class AdvancedScreenshotEditor {
  constructor(canvas, screenshot) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.screenshot = screenshot;
    this.currentTool = 'select';
    this.isDrawing = false;
    this.startX = 0;
    this.startY = 0;
    this.annotations = [];
    this.undoStack = [];
    this.redoStack = [];
    this.color = '#ff0000';
    this.size = 3;
    this.font = 'Arial';

    this.init();
  }

  async init() {
    // Load the screenshot image
    const img = new Image();
    img.onload = () => {
      this.canvas.width = img.width;
      this.canvas.height = img.height;
      this.ctx.drawImage(img, 0, 0);
      this.saveState();
    };
    img.src = this.screenshot.dataUrl;

    this.setupEventListeners();
  }

  setupEventListeners() {
    this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this));
    this.canvas.addEventListener('click', this.handleClick.bind(this));
  }

  handleMouseDown(e) {
    const rect = this.canvas.getBoundingClientRect();
    this.startX = (e.clientX - rect.left) * (this.canvas.width / rect.width);
    this.startY = (e.clientY - rect.top) * (this.canvas.height / rect.height);
    this.isDrawing = true;

    if (this.currentTool === 'draw') {
      this.ctx.beginPath();
      this.ctx.moveTo(this.startX, this.startY);
    }
  }

  handleMouseMove(e) {
    if (!this.isDrawing) return;

    const rect = this.canvas.getBoundingClientRect();
    const currentX = (e.clientX - rect.left) * (this.canvas.width / rect.width);
    const currentY = (e.clientY - rect.top) * (this.canvas.height / rect.height);

    if (this.currentTool === 'draw') {
      this.ctx.strokeStyle = this.color;
      this.ctx.lineWidth = this.size;
      this.ctx.lineCap = 'round';
      this.ctx.lineTo(currentX, currentY);
      this.ctx.stroke();
      this.ctx.beginPath();
      this.ctx.moveTo(currentX, currentY);
    }
  }

  handleMouseUp(e) {
    if (!this.isDrawing) return;
    this.isDrawing = false;

    const rect = this.canvas.getBoundingClientRect();
    const endX = (e.clientX - rect.left) * (this.canvas.width / rect.width);
    const endY = (e.clientY - rect.top) * (this.canvas.height / rect.height);

    switch (this.currentTool) {
      case 'arrow':
        this.drawArrow(this.startX, this.startY, endX, endY);
        break;
      case 'rectangle':
        this.drawRectangle(this.startX, this.startY, endX, endY);
        break;
      case 'circle':
        this.drawCircle(this.startX, this.startY, endX, endY);
        break;
      case 'highlight':
        this.drawHighlight(this.startX, this.startY, endX, endY);
        break;
      case 'blur':
        this.drawBlur(this.startX, this.startY, endX, endY);
        break;
    }

    if (this.currentTool !== 'select') {
      this.saveState();
    }
  }

  handleClick(e) {
    if (this.currentTool === 'text') {
      const rect = this.canvas.getBoundingClientRect();
      const x = (e.clientX - rect.left) * (this.canvas.width / rect.width);
      const y = (e.clientY - rect.top) * (this.canvas.height / rect.height);
      this.addText(x, y);
    }
  }

  setTool(tool) {
    this.currentTool = tool;
  }

  setColor(color) {
    this.color = color;
  }

  setSize(size) {
    this.size = size;
  }

  setFont(font) {
    this.font = font;
  }

  drawArrow(startX, startY, endX, endY) {
    this.ctx.strokeStyle = this.color;
    this.ctx.fillStyle = this.color;
    this.ctx.lineWidth = this.size;

    // Draw line
    this.ctx.beginPath();
    this.ctx.moveTo(startX, startY);
    this.ctx.lineTo(endX, endY);
    this.ctx.stroke();

    // Draw arrowhead
    const angle = Math.atan2(endY - startY, endX - startX);
    const arrowLength = 15;

    this.ctx.beginPath();
    this.ctx.moveTo(endX, endY);
    this.ctx.lineTo(
      endX - arrowLength * Math.cos(angle - Math.PI / 6),
      endY - arrowLength * Math.sin(angle - Math.PI / 6)
    );
    this.ctx.moveTo(endX, endY);
    this.ctx.lineTo(
      endX - arrowLength * Math.cos(angle + Math.PI / 6),
      endY - arrowLength * Math.sin(angle + Math.PI / 6)
    );
    this.ctx.stroke();
  }

  drawRectangle(startX, startY, endX, endY) {
    this.ctx.strokeStyle = this.color;
    this.ctx.lineWidth = this.size;
    this.ctx.strokeRect(
      Math.min(startX, endX),
      Math.min(startY, endY),
      Math.abs(endX - startX),
      Math.abs(endY - startY)
    );
  }

  drawCircle(startX, startY, endX, endY) {
    const radius = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
    this.ctx.strokeStyle = this.color;
    this.ctx.lineWidth = this.size;
    this.ctx.beginPath();
    this.ctx.arc(startX, startY, radius, 0, 2 * Math.PI);
    this.ctx.stroke();
  }

  drawHighlight(startX, startY, endX, endY) {
    this.ctx.fillStyle = 'rgba(255, 255, 0, 0.3)';
    this.ctx.fillRect(
      Math.min(startX, endX),
      Math.min(startY, endY),
      Math.abs(endX - startX),
      Math.abs(endY - startY)
    );
  }

  drawBlur(startX, startY, endX, endY) {
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(
      Math.min(startX, endX),
      Math.min(startY, endY),
      Math.abs(endX - startX),
      Math.abs(endY - startY)
    );
  }

  addText(x, y) {
    const text = prompt('Enter text:');
    if (text) {
      this.ctx.fillStyle = this.color;
      this.ctx.font = `${this.size * 4}px ${this.font}`;
      this.ctx.fillText(text, x, y);
      this.saveState();
    }
  }

  saveState() {
    this.undoStack.push(this.canvas.toDataURL());
    this.redoStack = []; // Clear redo stack when new action is performed

    // Limit undo stack size
    if (this.undoStack.length > 20) {
      this.undoStack.shift();
    }
  }

  undo() {
    if (this.undoStack.length > 1) {
      this.redoStack.push(this.undoStack.pop());
      this.restoreState(this.undoStack[this.undoStack.length - 1]);
    }
  }

  redo() {
    if (this.redoStack.length > 0) {
      const state = this.redoStack.pop();
      this.undoStack.push(state);
      this.restoreState(state);
    }
  }

  restoreState(dataUrl) {
    const img = new Image();
    img.onload = () => {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.drawImage(img, 0, 0);
    };
    img.src = dataUrl;
  }

  clearAll() {
    const img = new Image();
    img.onload = () => {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.drawImage(img, 0, 0);
      this.saveState();
    };
    img.src = this.screenshot.dataUrl;
  }

  getImageData() {
    return this.canvas.toDataURL('image/png');
  }

  destroy() {
    // Clean up event listeners and resources
    this.canvas.removeEventListener('mousedown', this.handleMouseDown);
    this.canvas.removeEventListener('mousemove', this.handleMouseMove);
    this.canvas.removeEventListener('mouseup', this.handleMouseUp);
    this.canvas.removeEventListener('click', this.handleClick);
  }
}

// Initialize the review manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
  new ScreenshotReviewManager();
});
