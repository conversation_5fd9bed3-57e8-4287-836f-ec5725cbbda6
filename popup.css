/* WorkflowCapture Pro - Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
  background: #ffffff;
  overflow: hidden;
}

.popup-container {
  width: 380px;
  height: 600px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* Header */
.popup-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.logo-text h1 {
  font-size: 16px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.tagline {
  font-size: 11px;
  color: #7f8c8d;
  margin: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: rgba(46, 204, 113, 0.1);
  border-radius: 12px;
}

.status-dot {
  width: 6px;
  height: 6px;
  background: #2ecc71;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-text {
  font-size: 11px;
  font-weight: 500;
  color: #27ae60;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Navigation */
.tab-navigation {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.tab-btn {
  flex: 1;
  padding: 12px 8px;
  border: none;
  background: transparent;
  color: #7f8c8d;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  position: relative;
}

.tab-btn:hover {
  color: #3498db;
  background: rgba(52, 152, 219, 0.05);
}

.tab-btn.active {
  color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #3498db;
}

/* Main Content */
.popup-content {
  flex: 1;
  background: white;
  overflow: hidden;
  position: relative;
}

.tab-content {
  display: none;
  height: 100%;
  overflow-y: auto;
  padding: 20px;
}

.tab-content.active {
  display: block;
}

/* Action Section */
.action-section {
  margin-bottom: 20px;
}

.primary-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

/* Buttons */
.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  flex: 1;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
  flex: 1;
}

.btn-secondary:hover {
  background: #d5dbdb;
}

.btn-outline {
  background: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Search */
.search-section {
  margin-bottom: 20px;
}

.search-input-container {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #95a5a6;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  font-size: 13px;
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Content Sections */
.content-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h3 {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.item-count {
  background: #e1e8ed;
  color: #7f8c8d;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #95a5a6;
}

.empty-state svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #7f8c8d;
}

.empty-state p {
  font-size: 12px;
  line-height: 1.4;
}

/* Workflow List */
.workflow-list, .notes-list {
  max-height: 300px;
  overflow-y: auto;
}

.workflow-item, .note-item {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.workflow-item:hover, .note-item:hover {
  background: #ecf0f1;
  border-color: #3498db;
  transform: translateY(-1px);
}

.workflow-title, .note-text {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  line-height: 1.3;
}

.workflow-meta, .note-meta {
  font-size: 11px;
  color: #7f8c8d;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workflow-steps {
  background: #3498db;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
}

/* Settings */
.settings-group {
  margin-bottom: 24px;
}

.settings-group h4 {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
}

.setting-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid #e1e8ed;
  border-radius: 3px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-top: 1px;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
  background: #3498db;
  border-color: #3498db;
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 4px;
  top: 1px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.setting-description {
  font-size: 11px;
  color: #7f8c8d;
  margin-top: 4px;
  margin-left: 28px;
  line-height: 1.4;
}

.export-buttons {
  display: flex;
  gap: 8px;
}

/* Footer */
.popup-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 12px 20px;
}

.footer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version {
  font-size: 11px;
  color: #95a5a6;
  font-weight: 500;
}

.help-link {
  font-size: 11px;
  color: #3498db;
  text-decoration: none;
  font-weight: 500;
}

.help-link:hover {
  text-decoration: underline;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #e1e8ed;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #bdc3c7;
}

/* Recording State */
.recording .status-indicator {
  background: rgba(231, 76, 60, 0.1);
}

.recording .status-dot {
  background: #e74c3c;
}

.recording .status-text {
  color: #c0392b;
}

.recording .btn-primary {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

/* Screenshot Items */
.screenshot-item {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.screenshot-item:hover {
  background: #ecf0f1;
  border-color: #3498db;
  transform: translateY(-1px);
}

.screenshot-preview {
  margin-bottom: 8px;
}

.screenshot-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #7f8c8d;
}

.screenshot-actions {
  display: flex;
  gap: 4px;
}

.btn-icon {
  background: transparent;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: #7f8c8d;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: #e1e8ed;
  color: #3498db;
}

/* Screenshot Editor Modal */
.screenshot-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.screenshot-editor-overlay {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.screenshot-editor-container {
  background: white;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.screenshot-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.screenshot-editor-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.close-editor {
  background: transparent;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: #7f8c8d;
  transition: all 0.2s ease;
}

.close-editor:hover {
  background: #e1e8ed;
  color: #e74c3c;
}

.screenshot-editor-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.tool-btn {
  background: white;
  border: 1px solid #e1e8ed;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  color: #7f8c8d;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-btn:hover {
  border-color: #3498db;
  color: #3498db;
}

.tool-btn.active {
  background: #3498db;
  border-color: #3498db;
  color: white;
}

.color-picker {
  margin-left: auto;
}

.color-picker input[type="color"] {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.screenshot-editor-canvas-container {
  padding: 20px;
  text-align: center;
  max-height: 500px;
  overflow: auto;
}

.screenshot-editor-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.secondary-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

/* Responsive adjustments */
@media (max-height: 500px) {
  .popup-container {
    height: 500px;
  }

  .tab-content {
    padding: 16px;
  }

  .workflow-list, .notes-list, .screenshots-list {
    max-height: 200px;
  }
}

@media (max-width: 600px) {
  .screenshot-editor-container {
    max-width: 95vw;
    max-height: 95vh;
  }

  .screenshot-editor-toolbar {
    flex-wrap: wrap;
    gap: 6px;
  }

  .tool-btn {
    padding: 6px;
  }
}
