/**
 * WorkflowCapture Pro - Content Script
 * Advanced workflow recording and playback system
 */

class WorkflowCapture {
  constructor() {
    this.isRecording = false;
    this.isPlayback = false;
    this.currentWorkflow = null;
    this.stepCounter = 0;
    this.recordedActions = [];
    this.lastScreenshot = null;
    this.mutationObserver = null;
    this.recordingIndicator = null;
    this.overlayControls = null;

    this.init();
  }

  async init() {
    // Get initial state from background
    const response = await this.sendMessage({ type: 'GET_CAPTURE_STATUS' });
    this.isRecording = response?.isCapturing || false;

    // Set up event listeners
    this.setupEventListeners();

    // Set up mutation observer for dynamic content
    this.setupMutationObserver();

    // Initialize UI if recording
    if (this.isRecording) {
      this.showRecordingUI();
    }

    console.log('WorkflowCapture Pro initialized');
  }

  setupEventListeners() {
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Capture user interactions
    document.addEventListener('click', this.handleClick.bind(this), true);
    document.addEventListener('input', this.handleInput.bind(this), true);
    document.addEventListener('change', this.handleChange.bind(this), true);
    document.addEventListener('submit', this.handleSubmit.bind(this), true);
    document.addEventListener('keydown', this.handleKeydown.bind(this), true);

    // Navigation events
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    window.addEventListener('load', this.handlePageLoad.bind(this));
  }

  setupMutationObserver() {
    this.mutationObserver = new MutationObserver((mutations) => {
      if (!this.isRecording) return;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Track dynamic content changes
          this.recordAction({
            type: 'dom_change',
            timestamp: Date.now(),
            mutation: {
              type: mutation.type,
              addedNodes: mutation.addedNodes.length,
              removedNodes: mutation.removedNodes.length,
              target: this.getElementSelector(mutation.target)
            }
          });
        }
      });
    });

    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeOldValue: true
    });
  }

  async handleMessage(message, sender, sendResponse) {
    switch (message.type) {
      case 'START_RECORDING':
        await this.startRecording();
        sendResponse({ success: true });
        break;

      case 'STOP_RECORDING':
        await this.stopRecording();
        sendResponse({ success: true });
        break;

      case 'CAPTURE_STATUS_UPDATE':
        this.isRecording = message.isCapturing;
        if (this.isRecording) {
          this.showRecordingUI();
        } else {
          this.hideRecordingUI();
        }
        sendResponse({ success: true });
        break;

      case 'START_PLAYBACK':
        await this.startPlayback(message.workflow);
        sendResponse({ success: true });
        break;

      case 'STOP_PLAYBACK':
        this.stopPlayback();
        sendResponse({ success: true });
        break;

      case 'CAPTURE_SELECTION':
        await this.captureSelection(message.selection);
        sendResponse({ success: true });
        break;

      default:
        sendResponse({ error: 'Unknown message type' });
    }
  }

  async startRecording() {
    this.isRecording = true;
    this.stepCounter = 0;
    this.recordedActions = [];
    this.currentWorkflow = {
      id: this.generateId(),
      title: document.title,
      url: window.location.href,
      startTime: Date.now(),
      steps: []
    };

    // Take initial screenshot
    await this.captureScreenshot('workflow_start');

    this.showRecordingUI();
    this.showNotification('Recording started', 'success');
  }

  async stopRecording() {
    if (!this.isRecording) return;

    this.isRecording = false;
    this.hideRecordingUI();

    // Process recorded actions into steps
    const processedWorkflow = await this.processWorkflow();

    // Save workflow
    await this.sendMessage({
      type: 'SAVE_WORKFLOW',
      workflow: processedWorkflow
    });

    this.showNotification('Workflow saved successfully', 'success');
    this.currentWorkflow = null;
    this.recordedActions = [];
  }

  async handleClick(event) {
    if (!this.isRecording) return;

    const element = event.target;
    const elementInfo = this.getElementInfo(element);

    // Highlight clicked element
    this.highlightElement(element);

    // Take screenshot before action
    await this.captureScreenshot('before_click');

    // Record the action
    this.recordAction({
      type: 'click',
      timestamp: Date.now(),
      element: elementInfo,
      coordinates: { x: event.clientX, y: event.clientY },
      modifiers: {
        ctrl: event.ctrlKey,
        shift: event.shiftKey,
        alt: event.altKey,
        meta: event.metaKey
      }
    });

    // Take screenshot after action (with delay for page changes)
    setTimeout(() => this.captureScreenshot('after_click'), 500);
  }

  async handleInput(event) {
    if (!this.isRecording) return;

    const element = event.target;
    const elementInfo = this.getElementInfo(element);

    this.recordAction({
      type: 'input',
      timestamp: Date.now(),
      element: elementInfo,
      value: element.value,
      inputType: event.inputType
    });
  }

  async handleChange(event) {
    if (!this.isRecording) return;

    const element = event.target;
    const elementInfo = this.getElementInfo(element);

    this.recordAction({
      type: 'change',
      timestamp: Date.now(),
      element: elementInfo,
      value: element.value,
      checked: element.checked
    });
  }

  async handleSubmit(event) {
    if (!this.isRecording) return;

    const form = event.target;
    const formInfo = this.getElementInfo(form);

    await this.captureScreenshot('before_submit');

    this.recordAction({
      type: 'submit',
      timestamp: Date.now(),
      element: formInfo,
      action: form.action,
      method: form.method
    });
  }

  async handleKeydown(event) {
    if (!this.isRecording) return;

    // Only record significant keystrokes
    const significantKeys = ['Enter', 'Tab', 'Escape', 'F1', 'F2', 'F3', 'F4', 'F5'];
    if (!significantKeys.includes(event.key) && !event.ctrlKey && !event.metaKey) return;

    this.recordAction({
      type: 'keydown',
      timestamp: Date.now(),
      key: event.key,
      code: event.code,
      modifiers: {
        ctrl: event.ctrlKey,
        shift: event.shiftKey,
        alt: event.altKey,
        meta: event.metaKey
      }
    });
  }

  handleBeforeUnload() {
    if (this.isRecording) {
      this.recordAction({
        type: 'navigation',
        timestamp: Date.now(),
        from: window.location.href,
        action: 'beforeunload'
      });
    }
  }

  handlePageLoad() {
    if (this.isRecording) {
      this.recordAction({
        type: 'navigation',
        timestamp: Date.now(),
        to: window.location.href,
        action: 'load'
      });
    }
  }

  recordAction(action) {
    this.recordedActions.push(action);
    this.stepCounter++;

    // Show step indicator
    if (action.element && action.element.selector) {
      this.showStepBadge(action.element.selector, this.stepCounter);
    }
  }

  getElementInfo(element) {
    return {
      tagName: element.tagName.toLowerCase(),
      id: element.id,
      className: element.className,
      selector: this.getElementSelector(element),
      text: element.textContent?.trim().substring(0, 100),
      attributes: this.getRelevantAttributes(element),
      position: this.getElementPosition(element)
    };
  }

  getElementSelector(element) {
    if (element.id) {
      return `#${element.id}`;
    }

    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.trim());
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes[0]}`;
      }
    }

    // Generate path-based selector
    const path = [];
    let current = element;

    while (current && current !== document.body) {
      let selector = current.tagName.toLowerCase();

      if (current.id) {
        selector += `#${current.id}`;
        path.unshift(selector);
        break;
      }

      const siblings = Array.from(current.parentNode?.children || []);
      const index = siblings.indexOf(current);
      if (index > 0) {
        selector += `:nth-child(${index + 1})`;
      }

      path.unshift(selector);
      current = current.parentNode;
    }

    return path.join(' > ');
  }

  getRelevantAttributes(element) {
    const relevantAttrs = ['type', 'name', 'value', 'placeholder', 'href', 'src', 'alt', 'title'];
    const attributes = {};

    relevantAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        attributes[attr] = element.getAttribute(attr);
      }
    });

    return attributes;
  }

  getElementPosition(element) {
    const rect = element.getBoundingClientRect();
    return {
      x: rect.left + window.scrollX,
      y: rect.top + window.scrollY,
      width: rect.width,
      height: rect.height
    };
  }

  highlightElement(element, duration = 2000) {
    element.classList.add('wcp-highlight');
    setTimeout(() => {
      element.classList.remove('wcp-highlight');
    }, duration);
  }

  showStepBadge(selector, stepNumber) {
    try {
      const element = document.querySelector(selector);
      if (!element) return;

      // Remove existing badge
      const existingBadge = element.querySelector('.wcp-step-badge');
      if (existingBadge) {
        existingBadge.remove();
      }

      const badge = document.createElement('div');
      badge.className = 'wcp-step-badge';
      badge.textContent = stepNumber;
      badge.setAttribute('aria-label', `Step ${stepNumber}`);

      element.style.position = 'relative';
      element.appendChild(badge);

      // Auto-remove after 3 seconds
      setTimeout(() => {
        badge.remove();
      }, 3000);
    } catch (error) {
      console.warn('Could not show step badge:', error);
    }
  }

  async captureScreenshot(context) {
    try {
      const response = await this.sendMessage({
        type: 'CAPTURE_SCREENSHOT',
        context: context,
        url: window.location.href,
        timestamp: Date.now()
      });

      this.lastScreenshot = response.screenshot;

      // Show flash effect
      this.showScreenshotFlash();

      return response.screenshot;
    } catch (error) {
      console.error('Screenshot capture failed:', error);
      return null;
    }
  }

  showScreenshotFlash() {
    const flash = document.createElement('div');
    flash.className = 'wcp-screenshot-flash';
    document.body.appendChild(flash);

    setTimeout(() => {
      flash.remove();
    }, 200);
  }

  showRecordingUI() {
    this.hideRecordingUI(); // Remove existing UI first

    // Create recording indicator
    this.recordingIndicator = document.createElement('div');
    this.recordingIndicator.className = 'wcp-recording-indicator';
    this.recordingIndicator.innerHTML = `
      <div class="wcp-recording-dot"></div>
      <span>Recording Workflow</span>
    `;
    this.recordingIndicator.setAttribute('aria-label', 'Workflow recording in progress');
    document.body.appendChild(this.recordingIndicator);

    // Create overlay controls
    this.overlayControls = document.createElement('div');
    this.overlayControls.className = 'wcp-overlay-controls';
    this.overlayControls.innerHTML = `
      <button class="wcp-control-btn" id="wcp-pause-btn">Pause</button>
      <button class="wcp-control-btn danger" id="wcp-stop-btn">Stop Recording</button>
      <span class="wcp-step-counter">Steps: <strong>0</strong></span>
    `;
    document.body.appendChild(this.overlayControls);

    // Add event listeners
    document.getElementById('wcp-pause-btn')?.addEventListener('click', () => {
      this.togglePause();
    });

    document.getElementById('wcp-stop-btn')?.addEventListener('click', () => {
      this.stopRecording();
    });

    // Show controls with animation
    setTimeout(() => {
      this.overlayControls.classList.add('show');
    }, 100);
  }

  hideRecordingUI() {
    if (this.recordingIndicator) {
      this.recordingIndicator.remove();
      this.recordingIndicator = null;
    }

    if (this.overlayControls) {
      this.overlayControls.remove();
      this.overlayControls = null;
    }

    // Remove all highlights and badges
    document.querySelectorAll('.wcp-highlight').forEach(el => {
      el.classList.remove('wcp-highlight');
    });

    document.querySelectorAll('.wcp-step-badge').forEach(badge => {
      badge.remove();
    });
  }

  togglePause() {
    // Implementation for pause/resume functionality
    const pauseBtn = document.getElementById('wcp-pause-btn');
    if (this.isRecording) {
      this.isRecording = false;
      pauseBtn.textContent = 'Resume';
      pauseBtn.classList.remove('wcp-control-btn');
      pauseBtn.classList.add('wcp-control-btn', 'success');
      this.showNotification('Recording paused', 'info');
    } else {
      this.isRecording = true;
      pauseBtn.textContent = 'Pause';
      pauseBtn.classList.remove('success');
      this.showNotification('Recording resumed', 'success');
    }
  }

  updateStepCounter() {
    const counter = document.querySelector('.wcp-step-counter strong');
    if (counter) {
      counter.textContent = this.stepCounter;
    }
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `wcp-notification wcp-notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 999999;
      background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      opacity: 0;
      transition: all 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(-50%) translateY(0)';
    }, 10);

    // Auto-remove
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(-50%) translateY(-20px)';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  async processWorkflow() {
    // Group actions into logical steps
    const steps = this.groupActionsIntoSteps(this.recordedActions);

    // Generate descriptions for each step
    const processedSteps = await Promise.all(
      steps.map(async (step, index) => ({
        id: this.generateId(),
        number: index + 1,
        description: await this.generateStepDescription(step),
        actions: step.actions,
        screenshot: step.screenshot,
        timestamp: step.timestamp,
        element: step.primaryElement
      }))
    );

    return {
      ...this.currentWorkflow,
      steps: processedSteps,
      endTime: Date.now(),
      totalSteps: processedSteps.length,
      duration: Date.now() - this.currentWorkflow.startTime
    };
  }

  groupActionsIntoSteps(actions) {
    const steps = [];
    let currentStep = null;

    actions.forEach(action => {
      // Start new step for significant actions
      if (this.isSignificantAction(action)) {
        if (currentStep) {
          steps.push(currentStep);
        }

        currentStep = {
          actions: [action],
          timestamp: action.timestamp,
          primaryElement: action.element,
          screenshot: null
        };
      } else if (currentStep) {
        // Add to current step
        currentStep.actions.push(action);
      }
    });

    if (currentStep) {
      steps.push(currentStep);
    }

    return steps;
  }

  isSignificantAction(action) {
    const significantTypes = ['click', 'submit', 'navigation', 'change'];
    return significantTypes.includes(action.type);
  }

  async generateStepDescription(step) {
    const primaryAction = step.actions[0];

    switch (primaryAction.type) {
      case 'click':
        return this.generateClickDescription(primaryAction);
      case 'input':
        return this.generateInputDescription(primaryAction);
      case 'submit':
        return this.generateSubmitDescription(primaryAction);
      case 'navigation':
        return this.generateNavigationDescription(primaryAction);
      default:
        return `Perform ${primaryAction.type} action`;
    }
  }

  generateClickDescription(action) {
    const element = action.element;
    const text = element.text || '';
    const tagName = element.tagName;

    if (tagName === 'button') {
      return `Click the "${text || 'button'}" button`;
    } else if (tagName === 'a') {
      return `Click the "${text || 'link'}" link`;
    } else if (element.attributes?.type === 'submit') {
      return `Click the "${text || 'Submit'}" button`;
    } else if (text) {
      return `Click on "${text}"`;
    } else {
      return `Click the ${tagName} element`;
    }
  }

  generateInputDescription(action) {
    const element = action.element;
    const placeholder = element.attributes?.placeholder;
    const name = element.attributes?.name;
    const type = element.attributes?.type || 'text';

    if (placeholder) {
      return `Enter text in the "${placeholder}" field`;
    } else if (name) {
      return `Enter text in the "${name}" ${type} field`;
    } else {
      return `Enter text in the ${type} field`;
    }
  }

  generateSubmitDescription(action) {
    return 'Submit the form';
  }

  generateNavigationDescription(action) {
    if (action.to) {
      return `Navigate to ${action.to}`;
    } else {
      return 'Navigate to new page';
    }
  }

  async startPlayback(workflow) {
    this.isPlayback = true;
    this.currentPlaybackWorkflow = workflow;
    this.currentPlaybackStep = 0;

    this.showPlaybackUI();
    await this.playNextStep();
  }

  stopPlayback() {
    this.isPlayback = false;
    this.hidePlaybackUI();
    this.currentPlaybackWorkflow = null;
    this.currentPlaybackStep = 0;
  }

  showPlaybackUI() {
    // Implementation for playback UI
    this.showNotification('Playback mode started', 'info');
  }

  hidePlaybackUI() {
    // Clean up playback UI
    document.querySelectorAll('.wcp-playback-highlight').forEach(el => {
      el.remove();
    });
  }

  async playNextStep() {
    if (!this.isPlayback || !this.currentPlaybackWorkflow) return;

    const step = this.currentPlaybackWorkflow.steps[this.currentPlaybackStep];
    if (!step) {
      this.stopPlayback();
      this.showNotification('Playback completed', 'success');
      return;
    }

    // Highlight the element for this step
    if (step.element?.selector) {
      this.highlightPlaybackElement(step.element.selector);
    }

    // Show step description
    this.showStepTooltip(step.description, step.element);

    this.currentPlaybackStep++;
  }

  highlightPlaybackElement(selector) {
    try {
      const element = document.querySelector(selector);
      if (element) {
        const highlight = document.createElement('div');
        highlight.className = 'wcp-playback-highlight';

        const rect = element.getBoundingClientRect();
        highlight.style.cssText = `
          position: absolute;
          top: ${rect.top + window.scrollY}px;
          left: ${rect.left + window.scrollX}px;
          width: ${rect.width}px;
          height: ${rect.height}px;
        `;

        document.body.appendChild(highlight);

        // Auto-remove after 3 seconds
        setTimeout(() => highlight.remove(), 3000);
      }
    } catch (error) {
      console.warn('Could not highlight playback element:', error);
    }
  }

  showStepTooltip(description, element) {
    if (!element?.selector) return;

    try {
      const targetElement = document.querySelector(element.selector);
      if (!targetElement) return;

      const tooltip = document.createElement('div');
      tooltip.className = 'wcp-tooltip show';
      tooltip.textContent = description;

      const rect = targetElement.getBoundingClientRect();
      tooltip.style.cssText = `
        position: absolute;
        top: ${rect.bottom + window.scrollY + 10}px;
        left: ${rect.left + window.scrollX}px;
      `;

      document.body.appendChild(tooltip);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        tooltip.classList.remove('show');
        setTimeout(() => tooltip.remove(), 200);
      }, 5000);
    } catch (error) {
      console.warn('Could not show step tooltip:', error);
    }
  }

  async captureSelection(selection) {
    const note = {
      text: selection,
      url: window.location.href,
      title: document.title,
      timestamp: Date.now()
    };

    await this.sendMessage({
      type: 'SAVE_NOTE',
      note: note
    });

    this.showNotification('Selection captured', 'success');
  }

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  async sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }
}

// Initialize the WorkflowCapture system
const workflowCapture = new WorkflowCapture();
