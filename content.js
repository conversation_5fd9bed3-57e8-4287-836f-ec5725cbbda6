// Content script for My Own Scribe extension
let isCapturing = false;
let lastSelection = null;

// Listen for capture state changes
chrome.runtime.onMessage.addListener((message) => {
  if (message.type === 'CAPTURE_STATUS_UPDATE') {
    isCapturing = message.isCapturing;
    if (!isCapturing && lastSelection) {
      clearSelectionHighlight();
    }
  }
});

// Get initial capture state
chrome.runtime.sendMessage({ type: 'GET_CAPTURE_STATUS' }, (response) => {
  isCapturing = response.isCapturing;
});

// Handle text selections
document.addEventListener('mouseup', () => {
  if (!isCapturing) return;
  
  const selection = window.getSelection();
  if (!selection || selection.isCollapsed) return;
  
  const selectedText = selection.toString().trim();
  if (!selectedText) return;
  
  lastSelection = selection;
  highlightSelection(selection);
  
  // Get context around selection
  const range = selection.getRangeAt(0);
  const context = getContextAroundRange(range);
  
  // Send note to background
  chrome.runtime.sendMessage({
    type: 'SAVE_NOTE',
    note: {
      text: selectedText,
      context: context,
      url: window.location.href,
      title: document.title
    }
  });
});

// Handle context menu captures
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'CAPTURE_SELECTION') {
    chrome.runtime.sendMessage({
      type: 'SAVE_NOTE',
      note: {
        text: message.selection,
        url: window.location.href,
        title: document.title
      }
    });
    sendResponse({ success: true });
  }
  return true;
});

// Highlight the selected text
function highlightSelection(selection) {
  const range = selection.getRangeAt(0);
  const span = document.createElement('span');
  span.style.backgroundColor = 'rgba(255, 255, 0, 0.3)';
  span.style.borderRadius = '2px';
  range.surroundContents(span);
}

// Clear highlight from last selection
function clearSelectionHighlight() {
  const highlights = document.querySelectorAll('span[style*="background-color: rgba(255, 255, 0, 0.3)"]');
  highlights.forEach(highlight => {
    const parent = highlight.parentNode;
    parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
    parent.normalize();
  });
  lastSelection = null;
}

// Get context around a selection range
function getContextAroundRange(range) {
  const startNode = range.startContainer;
  const endNode = range.endContainer;
  
  // If same node, get surrounding text
  if (startNode === endNode && startNode.nodeType === Node.TEXT_NODE) {
    const text = startNode.textContent;
    const start = Math.max(0, range.startOffset - 50);
    const end = Math.min(text.length, range.endOffset + 50);
    return text.slice(start, end).trim();
  }
  
  // Otherwise get parent element text
  const commonAncestor = range.commonAncestorContainer;
  if (commonAncestor.nodeType === Node.ELEMENT_NODE) {
    return commonAncestor.textContent.trim().slice(0, 200) + '...';
  }
  
  return '';
}
