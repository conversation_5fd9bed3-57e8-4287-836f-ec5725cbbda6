/* WorkflowCapture Pro - Content Script Styles */

/* Recording Indicator */
.wcp-recording-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 999999;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  animation: wcp-pulse 2s infinite;
  transition: all 0.3s ease;
}

.wcp-recording-indicator:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.wcp-recording-dot {
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  animation: wcp-blink 1s infinite;
}

@keyframes wcp-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes wcp-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* Element Highlighting */
.wcp-highlight {
  position: relative;
  transition: all 0.2s ease;
}

.wcp-highlight::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: rgba(52, 152, 219, 0.2);
  border: 2px solid #3498db;
  border-radius: 4px;
  pointer-events: none;
  z-index: 999998;
  animation: wcp-highlight-glow 0.3s ease-out;
}

@keyframes wcp-highlight-glow {
  0% {
    opacity: 0;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Step Number Badge */
.wcp-step-badge {
  position: absolute;
  top: -12px;
  left: -12px;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 11px;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
  z-index: 999999;
  animation: wcp-badge-appear 0.3s ease-out;
}

@keyframes wcp-badge-appear {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Tooltip */
.wcp-tooltip {
  position: absolute;
  background: rgba(44, 62, 80, 0.95);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 12px;
  font-weight: 500;
  max-width: 200px;
  z-index: 999999;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.2s ease;
  pointer-events: none;
}

.wcp-tooltip.show {
  opacity: 1;
  transform: translateY(0);
}

.wcp-tooltip::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid rgba(44, 62, 80, 0.95);
}

/* Overlay Controls */
.wcp-overlay-controls {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999999;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 12px;
  display: flex;
  gap: 8px;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  opacity: 0;
  transform: translateX(-50%) translateY(20px);
  transition: all 0.3s ease;
}

.wcp-overlay-controls.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.wcp-control-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.wcp-control-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.wcp-control-btn.danger {
  background: #e74c3c;
}

.wcp-control-btn.danger:hover {
  background: #c0392b;
}

.wcp-control-btn.success {
  background: #27ae60;
}

.wcp-control-btn.success:hover {
  background: #229954;
}

/* Screenshot Flash Effect */
.wcp-screenshot-flash {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  z-index: 999999;
  opacity: 0;
  pointer-events: none;
  animation: wcp-flash 0.2s ease-out;
}

@keyframes wcp-flash {
  0% { opacity: 0; }
  50% { opacity: 0.8; }
  100% { opacity: 0; }
}

/* Click Indicator Animation */
@keyframes wcp-click-pulse {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Playback Mode Styles */
.wcp-playback-mode {
  position: relative;
}

.wcp-playback-highlight {
  position: absolute;
  background: rgba(46, 204, 113, 0.2);
  border: 2px solid #2ecc71;
  border-radius: 4px;
  pointer-events: none;
  z-index: 999998;
  animation: wcp-playback-pulse 1s infinite;
}

@keyframes wcp-playback-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(46, 204, 113, 0);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .wcp-tooltip {
    background: rgba(255, 255, 255, 0.95);
    color: #2c3e50;
  }
  
  .wcp-tooltip::before {
    border-bottom-color: rgba(255, 255, 255, 0.95);
  }
  
  .wcp-overlay-controls {
    background: #2c3e50;
    color: white;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .wcp-recording-indicator {
    top: 10px;
    right: 10px;
    padding: 6px 12px;
    font-size: 11px;
  }
  
  .wcp-overlay-controls {
    bottom: 10px;
    padding: 8px;
  }
  
  .wcp-control-btn {
    padding: 6px 12px;
    font-size: 11px;
  }
}

/* Accessibility */
.wcp-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .wcp-highlight::before {
    border-width: 3px;
    background: rgba(0, 0, 255, 0.3);
    border-color: #0000ff;
  }
  
  .wcp-recording-indicator {
    background: #000000;
    border: 2px solid #ffffff;
  }
}
