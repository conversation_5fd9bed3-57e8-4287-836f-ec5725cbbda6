<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WorkflowCapture Pro</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <!-- Header -->
    <header class="popup-header">
      <div class="logo-section">
        <div class="logo-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="logo-text">
          <h1>WorkflowCapture Pro</h1>
          <p class="tagline">Intelligent workflow documentation</p>
        </div>
      </div>
      <div class="status-indicator" id="status-indicator">
        <div class="status-dot"></div>
        <span class="status-text">Ready</span>
      </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="tab-navigation">
      <button class="tab-btn active" data-tab="workflows">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        Workflows
      </button>
      <button class="tab-btn" data-tab="notes">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
          <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
        </svg>
        Notes
      </button>
      <button class="tab-btn" data-tab="settings">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="3"/>
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
        </svg>
        Settings
      </button>
    </nav>

    <!-- Main Content Area -->
    <main class="popup-content">
      <!-- Workflows Tab -->
      <div class="tab-content active" id="workflows-tab">
        <div class="action-section">
          <div class="primary-actions">
            <button class="btn btn-primary" id="toggle-recording">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <circle cx="12" cy="12" r="3"/>
              </svg>
              <span>Start Recording</span>
            </button>
            <button class="btn btn-secondary" id="import-workflow">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              Import
            </button>
          </div>
        </div>

        <div class="search-section">
          <div class="search-input-container">
            <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="workflow-search" placeholder="Search workflows..." class="search-input">
          </div>
        </div>

        <div class="content-section">
          <div class="section-header">
            <h3>Recent Workflows</h3>
            <span class="item-count" id="workflow-count">0</span>
          </div>
          <div class="workflow-list" id="workflow-list">
            <div class="empty-state">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <h4>No workflows yet</h4>
              <p>Start recording to create your first workflow</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes Tab -->
      <div class="tab-content" id="notes-tab">
        <div class="search-section">
          <div class="search-input-container">
            <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="notes-search" placeholder="Search notes..." class="search-input">
          </div>
        </div>

        <div class="action-section">
          <button class="btn btn-outline btn-sm" id="clear-notes">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="3,6 5,6 21,6"/>
              <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"/>
            </svg>
            Clear All
          </button>
        </div>

        <div class="content-section">
          <div class="section-header">
            <h3>Captured Notes</h3>
            <span class="item-count" id="notes-count">0</span>
          </div>
          <div class="notes-list" id="notes-list">
            <div class="empty-state">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
                <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
              </svg>
              <h4>No notes yet</h4>
              <p>Select text on any page to capture notes</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div class="tab-content" id="settings-tab">
        <div class="content-section">
          <div class="section-header">
            <h3>Preferences</h3>
          </div>

          <div class="settings-group">
            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" id="auto-screenshot" checked>
                <span class="checkmark"></span>
                Auto-capture screenshots
              </label>
              <p class="setting-description">Automatically take screenshots during workflow recording</p>
            </div>

            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" id="smart-descriptions" checked>
                <span class="checkmark"></span>
                Smart step descriptions
              </label>
              <p class="setting-description">Generate intelligent descriptions for workflow steps</p>
            </div>

            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" id="highlight-elements" checked>
                <span class="checkmark"></span>
                Highlight interactions
              </label>
              <p class="setting-description">Highlight elements during recording and playback</p>
            </div>
          </div>

          <div class="settings-group">
            <h4>Export Options</h4>
            <div class="export-buttons">
              <button class="btn btn-outline btn-sm" id="export-workflows">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
                Export Workflows
              </button>
              <button class="btn btn-outline btn-sm" id="export-notes">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
                Export Notes
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="popup-footer">
      <div class="footer-info">
        <span class="version">v1.0.0</span>
        <a href="#" class="help-link" id="help-link">Help & Support</a>
      </div>
    </footer>
  </div>

  <script src="popup.js"></script>
</body>
</html>
