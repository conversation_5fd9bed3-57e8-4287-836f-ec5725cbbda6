<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>My Own Scribe</title>
  <style>
    body {
      width: 300px;
      padding: 15px;
      font-family: 'Segoe UI', <PERSON>l, sans-serif;
      font-size: 14px;
      color: #333;
    }
    
    h1 {
      font-size: 18px;
      margin: 0 0 15px;
      color: #2c3e50;
      text-align: center;
    }
    
    button {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: none;
      border-radius: 4px;
      color: white;
      font-weight: bold;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    #toggle-capture {
      background-color: #3498db;
    }
    
    #toggle-capture:hover {
      background-color: #2980b9;
    }
    
    #clear-notes {
      background-color: #95a5a6;
    }
    
    #clear-notes:hover {
      background-color: #7f8c8d;
    }
    
    #notes-list {
      max-height: 400px;
      overflow-y: auto;
      margin-top: 15px;
      border-top: 1px solid #eee;
      padding-top: 10px;
    }
    
    .note {
      padding: 10px;
      margin-bottom: 10px;
      background-color: #f9f9f9;
      border-radius: 4px;
      border-left: 3px solid #3498db;
    }
    
    .note p {
      margin: 0 0 5px;
    }
    
    .note small {
      color: #7f8c8d;
      font-size: 12px;
    }
    
    .note a {
      color: #3498db;
      text-decoration: none;
    }
    
    .note a:hover {
      text-decoration: underline;
    }
    
    .empty-state {
      color: #95a5a6;
      text-align: center;
      font-style: italic;
      padding: 20px 0;
    }
  </style>
</head>
<body>
  <h1>My Own Scribe</h1>
  <input type="text" id="search-input" placeholder="Search notes..." style="
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
  ">
  <button id="toggle-capture">Start Capture</button>
  <button id="clear-notes">Clear All Notes</button>
  <div id="notes-list">
    <p class="empty-state">No notes yet. Start capturing to save selections.</p>
  </div>
  <script src="popup.js"></script>
</body>
</html>
