const fs = require('fs');
const PNG = require('pngjs').PNG;

[16, 32, 48, 128].forEach(size => {
  const png = new PNG({width: size, height: size});
  
  for (let y = 0; y < size; y++) {
    for (let x = 0; x < size; x++) {
      const idx = (size * y + x) << 2;
      png.data[idx] = 0;     // R
      png.data[idx+1] = 0;   // G
      png.data[idx+2] = 255; // B
      png.data[idx+3] = 255; // A
    }
  }
  
  fs.writeFileSync(`icons/icon${size}.png`, PNG.sync.write(png));
  console.log(`Created icon: icons/icon${size}.png`);
});
